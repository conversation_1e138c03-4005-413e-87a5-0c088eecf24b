#include "pch.h"

#include "OffsetsUpdater.hpp"
#include "offsets.hpp"
#include "../utils/network.hpp"
#include <sstream>
#include <algorithm>
#include <iostream>

std::string downloadFile( const std::string& url ) {
  return network::download_content( url );
}

Offsets parseOffsetsFromString(const std::string& data) {
  Offsets offsets;
  std::istringstream stream(data);
  std::string line;
  std::string prevLine;  // Speichert die vorherige Zeile

  while (std::getline(stream, line)) {
    // Suche nach Zeilen wie: constexpr std::ptrdiff_t <name> = <value>; // <type>
    if (line.find("constexpr") != std::string::npos) {
      std::istringstream iss(line);
      std::string type, dummy, name, equalSign;
      uint32_t value;

      iss >> type >> dummy >> name >> equalSign >> std::hex >> value;

      // Entferne unerwünschte Zeichen vom Namen (z. B. ';')
      name = name.substr(0, name.find('='));
      name.erase(std::remove_if(name.begin(), name.end(), ::isspace), name.end());

      // Verarbeite spezifische Offsets basierend auf umgebenden Werten
      if (name == "m_AttributeManager") {
        // Überprüfen, ob die vorherige Zeile "m_bAttributesInitialized" ist
        if (prevLine.find("m_bAttributesInitialized") != std::string::npos) {
          offsets.data[name] = value;  // Speichern von m_AttributeManager
        }
      } else if (name == "m_entitySpottedState") {
        // Überprüfen, ob die vorherige Zeile "m_vHeadConstraintOffset" ist
        if (prevLine.find("m_vHeadConstraintOffset") != std::string::npos) {
          offsets.data[name] = value;  // Speichern von m_entitySpottedState
        }
      } else {
        // Andere Offsets wie gewohnt speichern
        offsets.data[name] = value;
      }
    }
    prevLine = line;  // Setze die aktuelle Zeile als vorherige für die nächste Iteration
  }

  return offsets;
}


void useclientOffsets( const Offsets& offsets ) {
  try {
    Offset::Dormant      = offsets.get( "m_bDormant" );
    Offset::vecAbsOrigin = offsets.get( "m_vecAbsOrigin" );

    Offset::ActionTracking::perRoundStats          = offsets.get( "m_perRoundStats" );
    Offset::ActionTracking::matchStats             = offsets.get( "m_matchStats" );
    Offset::ActionTracking::NumRoundKills          = offsets.get( "m_iNumRoundKills" );
    Offset::ActionTracking::NumRoundKillsHeadshots = offsets.get( "m_iNumRoundKillsHeadshots" );
    Offset::ActionTracking::TotalRoundDamageDealt  = offsets.get( "m_unTotalRoundDamageDealt" );

    Offset::Entity::IsAlive       = offsets.get( "m_bPawnIsAlive" );
    Offset::Entity::PlayerPawn    = offsets.get( "m_hPlayerPawn" );
    Offset::Entity::iszPlayerName = offsets.get( "m_iszPlayerName" );
    Offset::Entity::GravityScale  = offsets.get( "m_flGravityScale" );

    Offset::Pawn::MovementServices  = offsets.get( "m_pMovementServices" );
    Offset::Pawn::WeaponServices    = offsets.get( "m_pWeaponServices" );
    Offset::Pawn::BulletServices    = offsets.get( "m_pBulletServices" );
    Offset::Pawn::CameraServices    = offsets.get( "m_pCameraServices" );
    Offset::Pawn::ViewModelServices = offsets.get( "m_pViewModelServices" );
    Offset::Pawn::pClippingWeapon   = offsets.get( "m_pClippingWeapon" );

    Offset::Pawn::ViewModel   = offsets.get( "m_hViewModel" );
    Offset::Pawn::CrouchState = offsets.get( "m_nCrouchState" );

    Offset::Pawn::isScoped   = offsets.get( "m_bIsScoped" );
    Offset::Pawn::isDefusing = offsets.get( "m_bIsDefusing" );
    Offset::Pawn::TotalHit   = offsets.get( "m_totalHitsOnServer" );
    Offset::Pawn::Pos        = offsets.get( "m_vOldOrigin" );
    // std::cout << "1.Offset::Pawn::Pos: 0x" << std::hex << Offset::Pawn::Pos << std::dec << std::endl;

    Offset::Pawn::CurrentArmor         = offsets.get( "m_ArmorValue" );
    Offset::Pawn::MaxHealth            = offsets.get( "m_iMaxHealth" );
    Offset::Pawn::CurrentHealth        = offsets.get( "m_iHealth" );
    Offset::Pawn::GameSceneNode        = offsets.get( "m_pGameSceneNode" );
    Offset::Pawn::BoneArray            = offsets.get( "m_modelState" );
    Offset::Pawn::angEyeAngles         = offsets.get( "m_angEyeAngles" );
    Offset::Pawn::vecLastClipCameraPos = offsets.get( "m_vecLastClipCameraPos" );
    Offset::Pawn::iShotsFired          = offsets.get( "m_iShotsFired" );
    Offset::Pawn::flFlashMaxAlpha      = offsets.get( "m_flFlashMaxAlpha" );
    Offset::Pawn::flFlashDuration      = offsets.get( "m_flFlashDuration" );
    Offset::Pawn::aimPunchAngle        = offsets.get( "m_aimPunchAngle" );
    Offset::Pawn::aimPunchCache        = offsets.get( "m_aimPunchCache" );
    Offset::Pawn::iIDEntIndex          = offsets.get( "m_iIDEntIndex" );
    Offset::Pawn::iTeamNum             = offsets.get( "m_iTeamNum" );
    Offset::Pawn::DesiredFov           = offsets.get( "m_iDesiredFOV" );
    Offset::Pawn::iFovStart            = offsets.get( "m_iFOVStart" );
    Offset::Pawn::fFlags               = offsets.get( "m_fFlags" );
    Offset::Pawn::bSpottedByMask       = offsets.get( "m_bSpottedByMask" );
    Offset::Pawn::entitySpottedState   = offsets.get( "m_entitySpottedState" );

    // std::cout << "Offset for m_entitySpottedState: 0x" << std::hex << Offset::Pawn::entitySpottedState << std::dec << std::endl;
    // std::cout << "Offset for m_bSpottedByMask: 0x" << std::hex << Offset::Pawn::bSpottedByMask << std::dec << std::endl;

    Offset::Pawn::AbsVelocity = offsets.get( "m_vecAbsVelocity" );
    Offset::Pawn::IsBuying    = offsets.get( "m_bIsBuyMenuOpen" );

    Offset::PlayerController::m_pActionTrackingServices = offsets.get( "m_pActionTrackingServices" );
    Offset::PlayerController::m_hPawn                   = offsets.get( "m_hPawn" );
    Offset::PlayerController::m_pObserverServices       = offsets.get( "m_pObserverServices" );
    Offset::PlayerController::m_hObserverTarget         = offsets.get( "m_hObserverTarget" );
    Offset::PlayerController::m_hController             = offsets.get( "m_hController" );
    Offset::PlayerController::PawnArmor                 = offsets.get( "m_iPawnArmor" );
    Offset::PlayerController::HasDefuser                = offsets.get( "m_bPawnHasDefuser" );
    Offset::PlayerController::HasHelmet                 = offsets.get( "m_bPawnHasHelmet" );

    // std::cout << "Offset for m_pActionTrackingServices: 0x" << std::hex << Offset::PlayerController::m_pActionTrackingServices << std::dec << std::endl;

    Offset::EconEntity::AttributeManager = offsets.get( "m_AttributeManager" );
    Offset::EconEntity::FallbackPaintKit = offsets.get( "m_nFallbackPaintKit" );
    Offset::EconEntity::FallbackSeed     = offsets.get( "m_nFallbackSeed" );
    Offset::EconEntity::FallbackWear     = offsets.get( "m_flFallbackWear" );
    Offset::EconEntity::FallbackStatTrak = offsets.get( "m_nFallbackStatTrak" );
    Offset::EconEntity::szCustomName     = offsets.get( "m_szCustomName" );
    Offset::EconEntity::EntityQuality    = offsets.get( "m_iEntityQuality" );
    Offset::EconEntity::ItemIDHigh       = offsets.get( "m_iItemIDHigh" );

    Offset::WeaponBaseData::szName              = offsets.get( "m_szName" );
    Offset::WeaponBaseData::Clip1               = offsets.get( "m_iClip1" );
    Offset::WeaponBaseData::MaxClip             = offsets.get( "m_iMaxClip1" );
    Offset::WeaponBaseData::CycleTime           = offsets.get( "m_flCycleTime" );
    Offset::WeaponBaseData::Penetration         = offsets.get( "m_flPenetration" );
    Offset::WeaponBaseData::WeaponType          = offsets.get( "m_WeaponType" );
    Offset::WeaponBaseData::Inaccuracy          = offsets.get( "m_flInaccuracyMove" );
    Offset::WeaponBaseData::inReload            = offsets.get( "m_bInReload" );
    Offset::WeaponBaseData::ActiveWeapon        = offsets.get( "m_hActiveWeapon" );
    Offset::WeaponBaseData::Item                = offsets.get( "m_Item" );
    Offset::WeaponBaseData::ItemDefinitionIndex = offsets.get( "m_iItemDefinitionIndex" );
    // std::cout << "Offset for itemDefinitionIndex: 0x" << std::hex << Offset::WeaponBaseData::ItemDefinitionIndex << std::dec << std::endl;
    Offset::WeaponBaseData::MeshGroupMask = offsets.get( "m_MeshGroupMask" );

    Offset::C4::bBeingDefused     = offsets.get( "m_bBeingDefused" );
    Offset::C4::flDefuseCountDown = offsets.get( "m_flDefuseCountDown" );
    Offset::C4::nBombSite         = offsets.get( "m_nBombSite" );

    Offset::InGameMoneyServices::MoneyServices      = offsets.get( "m_pInGameMoneyServices" );
    Offset::InGameMoneyServices::Account            = offsets.get( "m_iAccount" );
    Offset::InGameMoneyServices::TotalCashSpent     = offsets.get( "m_iTotalCashSpent" );
    Offset::InGameMoneyServices::CashSpentThisRound = offsets.get( "m_iCashSpentThisRound" );

    Offset::SmokeGrenadeProjectile::nSmokeEffectTickBegin    = offsets.get( "m_nSmokeEffectTickBegin" );
    Offset::SmokeGrenadeProjectile::bDidSmokeEffect          = offsets.get( "m_bDidSmokeEffect" );
    Offset::SmokeGrenadeProjectile::nRandomSeed              = offsets.get( "m_nRandomSeed" );
    Offset::SmokeGrenadeProjectile::vSmokeColor              = offsets.get( "m_vSmokeColor" );
    Offset::SmokeGrenadeProjectile::vSmokeDetonationPos      = offsets.get( "m_vSmokeDetonationPos" );
    Offset::SmokeGrenadeProjectile::VoxelFrameData           = offsets.get( "m_VoxelFrameData" );
    Offset::SmokeGrenadeProjectile::bSmokeVolumeDataReceived = offsets.get( "m_bSmokeVolumeDataReceived" );
    Offset::SmokeGrenadeProjectile::bSmokeEffectSpawned      = offsets.get( "m_bSmokeEffectSpawned" );

    // std::cout << "Offset for m_AttributeManager: 0x" << std::hex << Offset::EconEntity::AttributeManager << std::dec << std::endl;

    std::cout << "[+] Client offsets updated." << std::endl;

  } catch ( const std::exception& e ) {
    std::cerr << "[-] Failed to update client offsets: " << e.what() << std::endl;
    // Optional: Hier k�nntest du entscheiden, das Programm zu beenden oder Standardwerte zu verwenden
  }
}

void useOffsets( const Offsets& offsets ) {
  try {
    Offset::PlantedC4             = offsets.get( "dwPlantedC4" );
    Offset::EntityList            = offsets.get( "dwEntityList" );
    Offset::Matrix                = offsets.get( "dwViewMatrix" );
    Offset::LocalPlayerController = offsets.get( "dwLocalPlayerController" );
    Offset::LocalPlayerPawn       = offsets.get( "dwLocalPlayerPawn" );
    Offset::GlobalVars            = offsets.get( "dwGlobalVars" );
    Offset::ViewAngle             = offsets.get( "dwViewAngles" );

    // std::cout << "Offset for dwLocalPlayerController: 0x" << std::hex << Offset::LocalPlayerController << std::dec << std::endl;
    std::cout << "[+] Core offsets updated." << std::endl;

  } catch ( const std::exception& e ) {
    std::cerr << "[-] Failed to update core offsets: " << e.what() << std::endl;
  }
}

void usebuttonOffsets( const Offsets& offsets ) {
  try {
    // Stelle sicher, dass die Namen hier exakt mit den Namen in der heruntergeladenen buttons.hpp �bereinstimmen
    Offset::ForceJump    = offsets.get( "jump" );
    Offset::ForceCrouch  = offsets.get( "duck" );
    Offset::ForceForward = offsets.get( "forward" );    // Pr�fe, ob "forward" der korrekte Key ist
    Offset::ForceLeft    = offsets.get( "left" );   // Pr�fe, ob "moveleft" der korrekte Key ist
    Offset::ForceRight   = offsets.get( "right" );  // Pr�fe, ob "moveright" der korrekte Key ist
    // F�ge hier weitere Buttons hinzu, falls sie in offsets.hpp definiert sind
    // Beispiel: Offset::ForceAttack = offsets.get("attack");

    // std::cout << "Offset for jump: 0x" << std::hex << Offset::ForceJump << std::dec << std::endl;
    std::cout << "[+] Button offsets updated." << std::endl;

  } catch ( const std::exception& e ) {
    std::cerr << "[-] Failed to update button offsets: " << e.what() << std::endl;
  }
}
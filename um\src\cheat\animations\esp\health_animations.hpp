#pragma once
#include "../core/animation_manager.hpp"
#include <unordered_map>

class HealthAnimations {
public:
    // === MAIN API ===
    static float GetAnimatedHealth(int entityId, float targetHealth);
    static void ResetPlayerHealth(int entityId);
    static void ResetAllHealthAnimations();

    // === AUTOMATIC REGISTRATION ===
    // This automatically registers health bar animations when called
    static void UpdateHealthAnimation(int entityId, float targetHealth);

private:
    // === INTERNAL HELPERS ===
    static void RegisterHealthAnimation(int entityId, float currentHealth, float targetHealth);

    // === PREVIOUS VALUE TRACKING ===
    static std::unordered_map<int, float> previousHealthValues;
};

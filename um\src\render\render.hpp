#pragma once

#include <string>
#include "../../imgui/imgui.h" // Updated path to ImGui

enum font_flags_t
{
  none,
  dropshadow,
  outline
};

namespace Render {

  void AddRect( float x, float y, float w, float h, ImVec4 color, float rounding, float thickness );
  void fAddRect( float x, float y, float w, float h, ImVec4 color, float rounding, float thickness );

  void DrawRect( float x, float y, float w, float h, ImVec4 color, float rounding, float thickness );
  void fDrawRect( float x, float y, float w, float h, ImVec4 color, float rounding, float thickness );

  void DrawRectFilled( float x, float y, float w, float h, ImVec4 color, float rounding);

  void DrawRectFilledMultiColor( float x, float y, float w, float h, ImVec4 color, ImVec4 color2 );

  void DrawRectFilledMultiColorLandR( float x, float y, float w, float h, ImVec4 color, ImVec4 color2 );

  void Filled( float x, float y, float w, float h, ImVec4 color, float rounding, float thickness );

  void Line( float x1, float y1, float x2, float y2, ImVec4 color, float thickness );

  void fLine( float x1, float y1, float x2, float y2, ImVec4 color, float thickness );

  void Circle( float x, float y, float radius, ImVec4 color, float thickness );

  void Dot( float x, float y, float radius, ImVec4 color );
  void fDot( float x, float y, float radius, ImVec4 color );

  void Gun(float x, float y, ImVec4 color, std::string Text, float fontSize, int flag);

  void AALine(float x1, float y1, float x2, float y2, ImVec4 color, float thickness);

  void AACircle(float x, float y, float radius, ImVec4 color, float thickness);

  void AADot(float x, float y, float radius, ImVec4 color);

  void Text(float x, float y, ImVec4 color, std::string Text, float fontSize, int flag);

  // Statische Glow-Effekte
  void DrawRectGlow(float x, float y, float w, float h, ImVec4 innerColor, ImVec4 glowColor, float glowSize, float rounding);
  void fDrawRectGlow(float x, float y, float w, float h, ImVec4 innerColor, ImVec4 glowColor, float glowSize, float rounding);

  void DrawRectGlowMenu(float x, float y, float w, float h, ImVec4 innerColor, ImVec4 glowColor, float glowSize, float rounding);

  // Animierte Glow-Effekte mit zeitbasierter Transparenz
  void DrawRectGlowAnimated(float x, float y, float w, float h, ImVec4 innerColor, ImVec4 glowColor, float glowSize, float rounding, float pulseSpeed = 1.0f, float minAlpha = 0.1f, float maxAlpha = 1.0f);
  void fDrawRectGlowAnimated(float x, float y, float w, float h, ImVec4 innerColor, ImVec4 glowColor, float glowSize, float rounding, float pulseSpeed = 1.0f, float minAlpha = 0.1f, float maxAlpha = 1.0f);

} // namespace Render

#pragma once
#include "../core/animation_manager.hpp"
#include <unordered_map>

class ArmorAnimations {
public:
    // === MAIN API ===
    static float GetAnimatedArmor(int entityId, float targetArmor);
    static void ResetPlayerArmor(int entityId);
    static void ResetAllArmorAnimations();

    // === AUTOMATIC REGISTRATION ===
    // This automatically registers armor bar animations when called
    static void UpdateArmorAnimation(int entityId, float targetArmor);

private:
    // === INTERNAL HELPERS ===
    static void RegisterArmorAnimation(int entityId, float currentArmor, float targetArmor);

    // === PREVIOUS VALUE TRACKING ===
    static std::unordered_map<int, float> previousArmorValues;
};

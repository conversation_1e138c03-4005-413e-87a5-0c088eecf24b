#include "pch.h" // Ensure this is included at the very top of the file
#include "utils.hpp"
#include "../math/vector.hpp"
#include "../render/render.hpp"

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

std::string RemoveSuffix(const std::string& input, const std::string& deleate) {
  std::string result = input;
  std::string suffix = deleate;
  size_t      pos    = result.find(suffix);
  if (pos != std::string::npos) {
    result.erase(pos, suffix.length());
  }
  return result;
}

void HorizontalCircle(const Vector& origin, float radius, int segments, const view_matrix_t& viewMatrix, const ImVec4& color) {
  std::vector<Vector> circlePoints;
  circlePoints.reserve(segments + 1);

  // Generate points for the circle at ground level
  for (int i = 0; i <= segments; i++) {
    float theta = 2.0f * M_PI * float(i) / float(segments);

    Vector point;
    point.x = origin.x + radius * cos(theta);
    point.y = origin.y + radius * sin(theta);
    point.z = origin.z;  // Keep z-coordinate for ground level

    circlePoints.push_back(point);
  }

  // Render the circle segment by segment
  for (size_t i = 0; i < circlePoints.size() - 1; i++) {
    Vector screen1, screen2;
    if (Vector::world_to_screen(viewMatrix, circlePoints[i], screen1) &&
      Vector::world_to_screen(viewMatrix, circlePoints[i + 1], screen2)) {
      Render::Line(
        screen1.x, screen1.y,
        screen2.x, screen2.y,
        color,
        1.0f);
    }
  }
}

bool IsValidPosition(const Vector& vec) {
  // Check for NaN values
  if (std::isnan(vec.x) || std::isnan(vec.y) || std::isnan(vec.z)) {
    return false;
  }

  // Check for infinite values
  if (std::isinf(vec.x) || std::isinf(vec.y) || std::isinf(vec.z)) {
    return false;
  }

  // Check for reasonable coordinate ranges (CS2 map bounds are typically within these ranges)
  const float MAX_COORD = 32768.0f;
  if (std::abs(vec.x) > MAX_COORD || std::abs(vec.y) > MAX_COORD || std::abs(vec.z) > MAX_COORD) {
    return false;
  }

  return true;
}

float FastDistance3D(const Vector& pos1, const Vector& pos2) {
  float dx = pos1.x - pos2.x;
  float dy = pos1.y - pos2.y;
  float dz = pos1.z - pos2.z;

  // Use fast square root approximation for better performance
  return std::sqrt(dx * dx + dy * dy + dz * dz);
}

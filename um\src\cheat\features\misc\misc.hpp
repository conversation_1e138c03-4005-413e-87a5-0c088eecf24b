#pragma once
#include "../../../driver/driver.hpp"
#include "../../../math/vector.hpp"
#include "../../entity.hpp"
#include "../../gamevars.hpp"
#include "../../../render/render.hpp"
#include "../../globals.hpp"
#include "../../../window/window.hpp"
#include <string>

class Misc {
public:
  static void RenderMisc( const Reader& reader );
  static void Keystrokes(bool menuIsOpen = false);

private:
  // Drawing functions are private - using GameData for viewMatrix
  static void RecoilCrosshair();
  static void SniperCrosshair();
  static void Hitmarker();
  static void Sound();
  static void Watermark();
};

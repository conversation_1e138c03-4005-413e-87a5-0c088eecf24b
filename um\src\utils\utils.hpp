#pragma once
#include <string>

// Forward declarations
class Vector;
struct view_matrix_t;
struct ImVec4;

/**
 * @brief Entfernt einen bestimmten Suffix aus einem String
 * @param input Der Eingabe-String
 * @param deleate Der zu entfernende Suffix
 * @return String ohne den angegebenen Suffix
 */
std::string RemoveSuffix(const std::string& input, const std::string& deleate);

/**
 * @brief Zeichnet einen horizontalen Kreis in der 3D-Welt
 * @param origin Der Mittelpunkt des Kreises in Weltkoordinaten
 * @param radius Der Radius des Kreises
 * @param segments Die Anzahl der Segmente für die Kreis-Approximation
 * @param viewMatrix Die View-Matrix für die Welt-zu-Bildschirm-Transformation
 * @param color Die Farbe des Kreises
 */
void HorizontalCircle(const Vector& origin, float radius, int segments, const view_matrix_t& viewMatrix, const ImVec4& color);

/**
 * @brief Validates if a Vector contains valid position data
 * @param vec The vector to validate
 * @return true if vector is valid, false otherwise
 */
bool IsValidPosition(const Vector& vec);

/**
 * @brief Calculates distance between two 3D points efficiently
 * @param pos1 First position
 * @param pos2 Second position
 * @return Distance between the points
 */
float FastDistance3D(const Vector& pos1, const Vector& pos2);
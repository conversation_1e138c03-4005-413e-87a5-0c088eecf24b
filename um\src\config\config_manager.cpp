#include "pch.h"
#include "config_manager.hpp"
#include "../cheat/globals.hpp"

using json = nlohmann::json;
namespace fs = std::filesystem;

namespace ConfigManager {

    void ParseColor(const json& colorJson, ImVec4& color) {
        // Check if colorJson is null or empty first
        if (colorJson.is_null()) {
            color = ImVec4(0.0f, 0.0f, 0.0f, 1.0f);  // Default black color
            return;
        }

        // Use value() with default values for each component
        color.x = colorJson.value("R", 0.0f) / 255.0f;
        color.y = colorJson.value("G", 0.0f) / 255.0f;
        color.z = colorJson.value("B", 0.0f) / 255.0f;
        color.w = colorJson.value("A", 255.0f) / 255.0f;
    }

    json ColorToJson(const ImVec4& color) {
        return {
            {"R", static_cast<int>(color.x * 255.0f)},
            {"G", static_cast<int>(color.y * 255.0f)},
            {"B", static_cast<int>(color.z * 255.0f)},
            {"A", static_cast<int>(color.w * 255.0f)}
        };
    }

    std::string GetConfigPath() {
        char documentsPath[MAX_PATH];
        if (SHGetFolderPathA(NULL, CSIDL_MYDOCUMENTS, NULL, SHGFP_TYPE_CURRENT, documentsPath) == S_OK) {
            return std::string(documentsPath) + "\\Nebula\\cfg\\";
        }
        return ".\\cfg\\";  // Fallback to local directory
    }

    void EnsureConfigDirectories() {
        try {
            char documentsPath[MAX_PATH];
            if (SHGetFolderPathA(NULL, CSIDL_MYDOCUMENTS, NULL, SHGFP_TYPE_CURRENT, documentsPath) == S_OK) {
                fs::path nebulaPath = fs::path(documentsPath) / "Nebula";
                fs::path cfgPath = nebulaPath / "cfg";
                fs::path soundPath = nebulaPath / "sound";

                bool cfgCreated = false;
                bool soundCreated = false;

                // Only create if they don't exist
                if (!fs::exists(cfgPath)) {
                    fs::create_directories(cfgPath);
                    cfgCreated = true;
                }

                if (!fs::exists(soundPath)) {
                    fs::create_directories(soundPath);
                    soundCreated = true;
                }

                // Only show message if directories were actually created
                if (cfgCreated || soundCreated) {
                    std::cout << "Config directories created successfully" << std::endl;
                }
            }
        } catch (const fs::filesystem_error& e) {
            std::cerr << "Error creating config directories: " << e.what() << std::endl;
        }
    }

    void SaveConfig(const std::string& path, const std::string& filename) {
        std::string fullPath = path;
        if (!fullPath.empty() && fullPath.back() != '\\' && fullPath.back() != '/') {
            fullPath += '\\';
        }
        fullPath += filename;

        json j;

        // Triggerbot
        j["Triggerbot"]["Enabled"] = globals::Triggerbot::enabled;
        j["Triggerbot"]["Hotkey"] = globals::Triggerbot::hotkey;

        // Legitbot
        j["Legitbot"]["Enabled"] = globals::Legitbot::enabled;
        j["Legitbot"]["TeamCheck"] = globals::Legitbot::teamcheck;
        j["Legitbot"]["VisibleCheck"] = globals::Legitbot::visiblecheck;
        j["Legitbot"]["Smoothness"] = globals::Legitbot::smoothness;
        j["Legitbot"]["Radius"] = globals::Legitbot::radius;
        j["Legitbot"]["Circle"]["Enabled"] = globals::Legitbot::Circle::enabled;
        j["Legitbot"]["Circle"]["Filled"] = globals::Legitbot::Circle::filled;
        j["Legitbot"]["Circle"]["Color"] = ColorToJson(globals::Legitbot::Circle::Color);

        // ESP General
        j["ESP"]["Enabled"] = globals::Esp::enabled;
        j["ESP"]["IgnoreTeammates"] = globals::Esp::ignoreTeammates;

        // ESP settings (using existing globals::Esp namespace)
        j["ESP"]["Box"]["Enabled"] = globals::Esp::Box::enabled;
        j["ESP"]["Box"]["Color"] = ColorToJson(globals::Esp::Box::Color);
        j["ESP"]["Box"]["Type"] = globals::Esp::Box::type;
        j["ESP"]["Box"]["Rounding"] = globals::Esp::Box::rounding;
        j["ESP"]["Box"]["Length"] = globals::Esp::Box::length;
        j["ESP"]["Box"]["Outline"]["Enabled"] = globals::Esp::Box::outline;
        j["ESP"]["Box"]["Outline"]["Color"] = ColorToJson(globals::Esp::Box::outlineColor);
        j["ESP"]["Box"]["Spotted"]["Enabled"] = globals::Esp::Box::Spotted::enabled;
        j["ESP"]["Box"]["Spotted"]["Color"] = ColorToJson(globals::Esp::Box::Spotted::Color);
        j["ESP"]["Box"]["Filled"]["Enabled"] = globals::Esp::Box::Filled::enabled;
        j["ESP"]["Box"]["Filled"]["Color1"] = ColorToJson(globals::Esp::Box::Filled::Color);
        j["ESP"]["Box"]["Filled"]["Color2"] = ColorToJson(globals::Esp::Box::Filled::Color2);

        // ESP Health
        j["ESP"]["Health"]["Bar"]["Enabled"] = globals::Esp::Health::Bar::enabled;
        j["ESP"]["Health"]["Bar"]["Style"] = globals::Esp::Health::Bar::Style::type;
        j["ESP"]["Health"]["Bar"]["Color"] = ColorToJson(globals::Esp::Health::Bar::Color);
        j["ESP"]["Health"]["Bar"]["Glow"]["Enabled"] = globals::Esp::Health::Bar::Glow::enabled;
        j["ESP"]["Health"]["Value"]["Enabled"] = globals::Esp::Health::Value::enabled;
        j["ESP"]["Health"]["Value"]["Color"] = ColorToJson(globals::Esp::Health::Value::Color);

        // ESP Armor
        j["ESP"]["Armor"]["Bar"]["Enabled"] = globals::Esp::Armor::Bar::enabled;
        j["ESP"]["Armor"]["Bar"]["Color"] = ColorToJson(globals::Esp::Armor::Bar::Color);
        j["ESP"]["Armor"]["Bar"]["Glow"]["Enabled"] = globals::Esp::Armor::Bar::Glow::enabled;
        j["ESP"]["Armor"]["Value"]["Enabled"] = globals::Esp::Armor::Value::enabled;
        j["ESP"]["Armor"]["Value"]["Color"] = ColorToJson(globals::Esp::Armor::Value::Color);

        // ESP Skeleton
        j["ESP"]["Skeleton"]["Enabled"] = globals::Esp::Skeleton::enabled;
        j["ESP"]["Skeleton"]["Color"] = ColorToJson(globals::Esp::Skeleton::Color);
        j["ESP"]["Skeleton"]["Spotted"]["Enabled"] = globals::Esp::Skeleton::Spotted::enabled;
        j["ESP"]["Skeleton"]["Spotted"]["Color"] = ColorToJson(globals::Esp::Skeleton::Spotted::Color);
        j["ESP"]["Skeleton"]["Head"]["Enabled"] = globals::Esp::Skeleton::Head::enabled;
        j["ESP"]["Skeleton"]["Head"]["Color"] = ColorToJson(globals::Esp::Skeleton::Head::Color);
        j["ESP"]["Skeleton"]["Dots"]["Enabled"] = globals::Esp::Skeleton::Dots::enabled;
        j["ESP"]["Skeleton"]["Dots"]["Color"] = ColorToJson(globals::Esp::Skeleton::Dots::Color);
        j["ESP"]["Skeleton"]["Dots"]["Size"] = globals::Esp::Skeleton::Dots::radius;

        // ESP Snapline
        j["ESP"]["Snapline"]["Enabled"] = globals::Esp::Snapline::enabled;
        j["ESP"]["Snapline"]["Color"] = ColorToJson(globals::Esp::Snapline::Color);
        j["ESP"]["Snapline"]["Spotted"]["Enabled"] = globals::Esp::Snapline::Spotted::enabled;
        j["ESP"]["Snapline"]["Spotted"]["Color"] = ColorToJson(globals::Esp::Snapline::Spotted::Color);
        j["ESP"]["Snapline"]["Thickness"] = globals::Esp::Snapline::thickness;
        j["ESP"]["Snapline"]["Start"] = globals::Esp::Snapline::Start;
        j["ESP"]["Snapline"]["End"]["Upper"] = globals::Esp::Snapline::End::upper;
        j["ESP"]["Snapline"]["End"]["Head"] = globals::Esp::Snapline::End::head;
        j["ESP"]["Snapline"]["End"]["Center"] = globals::Esp::Snapline::End::center;
        j["ESP"]["Snapline"]["End"]["Lower"] = globals::Esp::Snapline::End::lower;

        // ESP Info
        j["ESP"]["Info"]["Enabled"] = globals::Esp::Info::enabled;
        j["ESP"]["Info"]["Color"] = ColorToJson(globals::Esp::Info::Color);
        j["ESP"]["Info"]["Spotted"]["Enabled"] = globals::Esp::Info::Spotted::enabled;
        j["ESP"]["Info"]["Spotted"]["Color"] = ColorToJson(globals::Esp::Info::Spotted::Color);
        j["ESP"]["Info"]["Name"]["Player"] = globals::Esp::Info::Name::player;
        j["ESP"]["Info"]["Name"]["Weapon"] = globals::Esp::Info::Name::weapon;
        j["ESP"]["Info"]["Icon"]["Enabled"] = globals::Esp::Info::Icon::enabled;
        j["ESP"]["Info"]["State"] = globals::Esp::Info::state;

        // ESP Viewline
        j["ESP"]["Viewline"]["Enabled"] = globals::Esp::Viewline::enabled;
        j["ESP"]["Viewline"]["Color"] = ColorToJson(globals::Esp::Viewline::Color);
        j["ESP"]["Viewline"]["Spotted"]["Enabled"] = globals::Esp::Viewline::Spotted::enabled;
        j["ESP"]["Viewline"]["Spotted"]["Color"] = ColorToJson(globals::Esp::Viewline::Spotted::Color);
        j["ESP"]["Viewline"]["Dot"]["Enabled"] = globals::Esp::Viewline::Dot::enabled;
        j["ESP"]["Viewline"]["Dot"]["Color"] = ColorToJson(globals::Esp::Viewline::Dot::Color);
        j["ESP"]["Viewline"]["Length"] = globals::Esp::Viewline::length;

        // ESP Death animations
        j["ESP"]["Death"]["Enabled"] = globals::Esp::Death::enabled;
        j["ESP"]["Death"]["Duration"] = globals::Esp::Death::duration;
        j["ESP"]["Death"]["Color"] = ColorToJson(globals::Esp::Death::Color);
        j["ESP"]["Death"]["Box"]["Enabled"] = globals::Esp::Death::Box::enabled;
        j["ESP"]["Death"]["Skeleton"]["Enabled"] = globals::Esp::Death::Skeleton::enabled;
        j["ESP"]["Death"]["Info"]["Enabled"] = globals::Esp::Death::Info::enabled;
        j["ESP"]["Death"]["Health"]["Enabled"] = globals::Esp::Death::Health::enabled;
        j["ESP"]["Death"]["Armor"]["Enabled"] = globals::Esp::Death::Armor::enabled;
        j["ESP"]["Death"]["Snapline"]["Enabled"] = globals::Esp::Death::Snapline::enabled;

        // Other features
        j["Others"]["DarkMode"]["Enabled"] = globals::DarkMode::enabled;
        j["Others"]["DarkMode"]["Alpha"] = globals::DarkMode::alpha;
        j["Others"]["Projectile"]["Enabled"] = globals::Projectile::enabled;
        j["Others"]["Projectile"]["Color"] = ColorToJson(globals::Projectile::Color);
        j["Others"]["Projectile"]["Line"] = globals::Projectile::line;
        j["Others"]["Projectile"]["Name"] = globals::Projectile::name;
        j["Others"]["Projectile"]["Box"] = globals::Projectile::box;
        j["Others"]["Projectile"]["Erase"] = globals::Projectile::erase;

        // Smoke
        j["Others"]["Smoke"]["Enabled"] = globals::Smoke::enabled;
        j["Others"]["Smoke"]["Name"]["Enabled"] = globals::Smoke::name::enabled;
        j["Others"]["Smoke"]["Name"]["Color"] = ColorToJson(globals::Smoke::name::Color);
        j["Others"]["Smoke"]["Circle"]["Enabled"] = globals::Smoke::circle::enabled;
        j["Others"]["Smoke"]["Circle"]["Color"] = ColorToJson(globals::Smoke::circle::Color);
        j["Others"]["Smoke"]["Countdown"]["Enabled"] = globals::Smoke::countdown::enabled;
        j["Others"]["Smoke"]["Countdown"]["Bar"]["Enabled"] = globals::Smoke::countdown::bar::enabled;
        j["Others"]["Smoke"]["Countdown"]["Bar"]["Color"] = ColorToJson(globals::Smoke::countdown::bar::Color);
        j["Others"]["Smoke"]["Countdown"]["Text"]["Enabled"] = globals::Smoke::countdown::text::enabled;
        j["Others"]["Smoke"]["Countdown"]["Text"]["Color"] = ColorToJson(globals::Smoke::countdown::text::Color);

        // Misc features
        j["Misc"]["Hitmarker"]["Enabled"] = globals::Hitmarker::enabled;
        j["Misc"]["Hitmarker"]["Color"] = ColorToJson(globals::Hitmarker::Color);
        j["Misc"]["Hitmarker"]["Thickness"] = globals::Hitmarker::thickness;
        j["Misc"]["Hitmarker"]["Length"] = globals::Hitmarker::length;
        j["Misc"]["Hitmarker"]["Gap"] = globals::Hitmarker::gap;
        j["Misc"]["Hitmarker"]["Duration"] = globals::Hitmarker::duration;

        j["Misc"]["Sound"]["Enabled"] = globals::Sound::enabled;
        j["Misc"]["Sound"]["Volume"] = globals::Sound::volume;
        j["Misc"]["Sound"]["Hit"]["Enabled"] = globals::Hitsound::enabled;
        j["Misc"]["Sound"]["Hit"]["Sound"] = globals::Hitsound::sound;
        j["Misc"]["Sound"]["Kill"]["Enabled"] = globals::Killsound::enabled;
        j["Misc"]["Sound"]["Kill"]["Sound"] = globals::Killsound::sound;
        j["Misc"]["Sound"]["Kill"]["Hs"]["Enabled"] = globals::HsKillsound::enabled;
        j["Misc"]["Sound"]["Kill"]["Hs"]["Sound"] = globals::HsKillsound::sound;

        j["Misc"]["NoScope Crosshair"]["Enabled"] = globals::Crosshair::enabled;
        j["Misc"]["NoScope Crosshair"]["Color"] = ColorToJson(globals::Crosshair::Color);
        j["Misc"]["NoScope Crosshair"]["Thickness"] = globals::Crosshair::thickness;
        j["Misc"]["NoScope Crosshair"]["Length"] = globals::Crosshair::length;
        j["Misc"]["NoScope Crosshair"]["Gap"] = globals::Crosshair::gap;
        j["Misc"]["NoScope Crosshair"]["Dot"]["Enabled"] = globals::Crosshair::dotenabled;
        j["Misc"]["NoScope Crosshair"]["Dot"]["Size"] = globals::Crosshair::dotSize;

        j["Misc"]["Keystrokes"]["Enabled"] = globals::Keystrokes::enabled;
        j["Misc"]["Keystrokes"]["Color1"] = ColorToJson(globals::Keystrokes::Color);
        j["Misc"]["Keystrokes"]["Color2"] = ColorToJson(globals::Keystrokes::pressedColor);
        j["Misc"]["Keystrokes"]["Scale"] = globals::Keystrokes::scale;
        j["Misc"]["Keystrokes"]["PosX"] = globals::Keystrokes::posX;
        j["Misc"]["Keystrokes"]["PosY"] = globals::Keystrokes::posY;

        j["Misc"]["RecoilCrosshair"]["Enabled"] = globals::Misc::RecoilCrosshair::enabled;
        j["Misc"]["RecoilCrosshair"]["Size"] = globals::Misc::RecoilCrosshair::size;
        j["Misc"]["RecoilCrosshair"]["Color"] = ColorToJson(globals::Misc::RecoilCrosshair::Color);

        j["Misc"]["Watermark"]["Enabled"] = globals::Watermark::enabled;
        j["Misc"]["Watermark"]["TextColor"] = ColorToJson(globals::Watermark::Color);
        j["Misc"]["Watermark"]["GradientColor"] = ColorToJson(globals::Watermark::gradientColor);

        // Menu
        j["Menu"]["Color"] = ColorToJson(globals::MenuOutline::Color);

        // No auto-load settings needed

        std::ofstream file(fullPath);
        if (file.is_open()) {
            file << std::setw(4) << j << std::endl;
            file.close();
            std::cout << "Successfully saved config to: " << fullPath << std::endl;
        } else {
            std::cerr << "Failed to save config to: " << fullPath << std::endl;
        }
    }

    void LoadConfig(const std::string& path, const std::string& filename) {
        std::string fullPath = path;
        if (!fullPath.empty() && fullPath.back() != '\\' && fullPath.back() != '/') {
            fullPath += '\\';
        }
        fullPath += filename;

        std::ifstream file(fullPath);
        if (!file.is_open()) {
            std::cerr << "Error opening config file: " << fullPath << std::endl;
            CreateDefaultConfig();
            return;
        }

        try {
            json j;
            file >> j;

            // Triggerbot
            if (j.contains("Triggerbot")) {
                globals::Triggerbot::enabled = j["Triggerbot"].value("Enabled", false);
                globals::Triggerbot::hotkey = j["Triggerbot"].value("Hotkey", VK_LMENU);
            }

            // Legitbot
            if (j.contains("Legitbot")) {
                globals::Legitbot::enabled = j["Legitbot"].value("Enabled", true);
                globals::Legitbot::teamcheck = j["Legitbot"].value("TeamCheck", true);
                globals::Legitbot::visiblecheck = j["Legitbot"].value("VisibleCheck", true);
                globals::Legitbot::smoothness = j["Legitbot"].value("Smoothness", 1.0f);
                globals::Legitbot::radius = j["Legitbot"].value("Radius", 99.9f);

                if (j["Legitbot"].contains("Circle")) {
                    globals::Legitbot::Circle::enabled = j["Legitbot"]["Circle"].value("Enabled", true);
                    globals::Legitbot::Circle::filled = j["Legitbot"]["Circle"].value("Filled", true);
                    ParseColor(j["Legitbot"]["Circle"]["Color"], globals::Legitbot::Circle::Color);
                }
            }

            // ESP General
            if (j.contains("ESP")) {
                globals::Esp::enabled = j["ESP"].value("Enabled", false);
                globals::Esp::ignoreTeammates = j["ESP"].value("IgnoreTeammates", true);
            }

            // ESP settings (updated to match existing structure)
            if (j.contains("ESP") && j["ESP"].is_object()) {

                if (j["ESP"].contains("Box")) {
                    globals::Esp::Box::enabled = j["ESP"]["Box"].value("Enabled", false);
                    ParseColor(j["ESP"]["Box"]["Color"], globals::Esp::Box::Color);
                    globals::Esp::Box::type = j["ESP"]["Box"].value("Type", 0);
                    globals::Esp::Box::rounding = j["ESP"]["Box"].value("Rounding", 0.0f);
                    globals::Esp::Box::length = j["ESP"]["Box"].value("Length", 0.3f);

                    if (j["ESP"]["Box"].contains("Outline")) {
                        globals::Esp::Box::outline = j["ESP"]["Box"]["Outline"].value("Enabled", false);
                        ParseColor(j["ESP"]["Box"]["Outline"]["Color"], globals::Esp::Box::outlineColor);
                    }

                    if (j["ESP"]["Box"].contains("Spotted")) {
                        globals::Esp::Box::Spotted::enabled = j["ESP"]["Box"]["Spotted"].value("Enabled", false);
                        ParseColor(j["ESP"]["Box"]["Spotted"]["Color"], globals::Esp::Box::Spotted::Color);
                    }

                    if (j["ESP"]["Box"].contains("Filled")) {
                        globals::Esp::Box::Filled::enabled = j["ESP"]["Box"]["Filled"].value("Enabled", false);
                        ParseColor(j["ESP"]["Box"]["Filled"]["Color1"], globals::Esp::Box::Filled::Color);
                        ParseColor(j["ESP"]["Box"]["Filled"]["Color2"], globals::Esp::Box::Filled::Color2);
                    }
                }

                if (j["ESP"].contains("Health")) {
                    globals::Esp::Health::Bar::enabled = j["ESP"]["Health"]["Bar"].value("Enabled", false);
                    globals::Esp::Health::Bar::Style::type = j["ESP"]["Health"]["Bar"].value("Style", 0);
                    ParseColor(j["ESP"]["Health"]["Bar"]["Color"], globals::Esp::Health::Bar::Color);
                    globals::Esp::Health::Bar::Glow::enabled = j["ESP"]["Health"]["Bar"]["Glow"].value("Enabled", false);
                    globals::Esp::Health::Value::enabled = j["ESP"]["Health"]["Value"].value("Enabled", false);
                    if (j["ESP"]["Health"]["Value"].contains("Color")) {
                        ParseColor(j["ESP"]["Health"]["Value"]["Color"], globals::Esp::Health::Value::Color);
                    }
                }

                if (j["ESP"].contains("Armor")) {
                    globals::Esp::Armor::Bar::enabled = j["ESP"]["Armor"]["Bar"].value("Enabled", false);
                    ParseColor(j["ESP"]["Armor"]["Bar"]["Color"], globals::Esp::Armor::Bar::Color);
                    globals::Esp::Armor::Bar::Glow::enabled = j["ESP"]["Armor"]["Bar"]["Glow"].value("Enabled", false);
                    globals::Esp::Armor::Value::enabled = j["ESP"]["Armor"]["Value"].value("Enabled", false);
                    if (j["ESP"]["Armor"]["Value"].contains("Color")) {
                        ParseColor(j["ESP"]["Armor"]["Value"]["Color"], globals::Esp::Armor::Value::Color);
                    }
                }

                if (j["ESP"].contains("Skeleton")) {
                    globals::Esp::Skeleton::enabled = j["ESP"]["Skeleton"].value("Enabled", false);
                    ParseColor(j["ESP"]["Skeleton"]["Color"], globals::Esp::Skeleton::Color);

                    if (j["ESP"]["Skeleton"].contains("Spotted")) {
                        globals::Esp::Skeleton::Spotted::enabled = j["ESP"]["Skeleton"]["Spotted"].value("Enabled", false);
                        ParseColor(j["ESP"]["Skeleton"]["Spotted"]["Color"], globals::Esp::Skeleton::Spotted::Color);
                    }

                    if (j["ESP"]["Skeleton"].contains("Head")) {
                        globals::Esp::Skeleton::Head::enabled = j["ESP"]["Skeleton"]["Head"].value("Enabled", false);
                        ParseColor(j["ESP"]["Skeleton"]["Head"]["Color"], globals::Esp::Skeleton::Head::Color);
                    }

                    if (j["ESP"]["Skeleton"].contains("Dots")) {
                        globals::Esp::Skeleton::Dots::enabled = j["ESP"]["Skeleton"]["Dots"].value("Enabled", false);
                        ParseColor(j["ESP"]["Skeleton"]["Dots"]["Color"], globals::Esp::Skeleton::Dots::Color);
                        globals::Esp::Skeleton::Dots::radius = j["ESP"]["Skeleton"]["Dots"].value("Size", 1.0f);
                    }
                }

                if (j["ESP"].contains("Snapline")) {
                    globals::Esp::Snapline::enabled = j["ESP"]["Snapline"].value("Enabled", false);
                    ParseColor(j["ESP"]["Snapline"]["Color"], globals::Esp::Snapline::Color);

                    if (j["ESP"]["Snapline"].contains("Spotted")) {
                        globals::Esp::Snapline::Spotted::enabled = j["ESP"]["Snapline"]["Spotted"].value("Enabled", false);
                        ParseColor(j["ESP"]["Snapline"]["Spotted"]["Color"], globals::Esp::Snapline::Spotted::Color);
                    }

                    globals::Esp::Snapline::thickness = j["ESP"]["Snapline"].value("Thickness", 1.0f);
                    globals::Esp::Snapline::Start = j["ESP"]["Snapline"].value("Start", "UPPER");

                    if (j["ESP"]["Snapline"].contains("End")) {
                        globals::Esp::Snapline::End::upper = j["ESP"]["Snapline"]["End"].value("Upper", false);
                        globals::Esp::Snapline::End::head = j["ESP"]["Snapline"]["End"].value("Head", false);
                        globals::Esp::Snapline::End::center = j["ESP"]["Snapline"]["End"].value("Center", true);
                        globals::Esp::Snapline::End::lower = j["ESP"]["Snapline"]["End"].value("Lower", false);
                    }
                }

                if (j["ESP"].contains("Info")) {
                    globals::Esp::Info::enabled = j["ESP"]["Info"].value("Enabled", false);
                    ParseColor(j["ESP"]["Info"]["Color"], globals::Esp::Info::Color);

                    if (j["ESP"]["Info"].contains("Spotted")) {
                        globals::Esp::Info::Spotted::enabled = j["ESP"]["Info"]["Spotted"].value("Enabled", false);
                        ParseColor(j["ESP"]["Info"]["Spotted"]["Color"], globals::Esp::Info::Spotted::Color);
                    }

                    if (j["ESP"]["Info"].contains("Name")) {
                        globals::Esp::Info::Name::player = j["ESP"]["Info"]["Name"].value("Player", false);
                        globals::Esp::Info::Name::weapon = j["ESP"]["Info"]["Name"].value("Weapon", false);
                    }

                    if (j["ESP"]["Info"].contains("Icon")) {
                        globals::Esp::Info::Icon::enabled = j["ESP"]["Info"]["Icon"].value("Enabled", false);
                    }

                    globals::Esp::Info::state = j["ESP"]["Info"].value("State", false);
                }

                if (j["ESP"].contains("Viewline")) {
                    globals::Esp::Viewline::enabled = j["ESP"]["Viewline"].value("Enabled", false);
                    ParseColor(j["ESP"]["Viewline"]["Color"], globals::Esp::Viewline::Color);

                    if (j["ESP"]["Viewline"].contains("Spotted")) {
                        globals::Esp::Viewline::Spotted::enabled = j["ESP"]["Viewline"]["Spotted"].value("Enabled", false);
                        ParseColor(j["ESP"]["Viewline"]["Spotted"]["Color"], globals::Esp::Viewline::Spotted::Color);
                    }

                    if (j["ESP"]["Viewline"].contains("Dot")) {
                        globals::Esp::Viewline::Dot::enabled = j["ESP"]["Viewline"]["Dot"].value("Enabled", false);
                        ParseColor(j["ESP"]["Viewline"]["Dot"]["Color"], globals::Esp::Viewline::Dot::Color);
                    }

                    globals::Esp::Viewline::length = j["ESP"]["Viewline"].value("Length", 100.0f);
                }

                if (j["ESP"].contains("Death")) {
                    globals::Esp::Death::enabled = j["ESP"]["Death"].value("Enabled", false);
                    globals::Esp::Death::duration = j["ESP"]["Death"].value("Duration", 1.0f);
                    ParseColor(j["ESP"]["Death"]["Color"], globals::Esp::Death::Color);
                    globals::Esp::Death::Box::enabled = j["ESP"]["Death"]["Box"].value("Enabled", false);
                    globals::Esp::Death::Skeleton::enabled = j["ESP"]["Death"]["Skeleton"].value("Enabled", false);
                    globals::Esp::Death::Info::enabled = j["ESP"]["Death"]["Info"].value("Enabled", false);
                    globals::Esp::Death::Health::enabled = j["ESP"]["Death"]["Health"].value("Enabled", false);
                    globals::Esp::Death::Armor::enabled = j["ESP"]["Death"]["Armor"].value("Enabled", false);
                    globals::Esp::Death::Snapline::enabled = j["ESP"]["Death"]["Snapline"].value("Enabled", false);
                }
            }

            // Load other features
            if (j.contains("Others")) {
                if (j["Others"].contains("DarkMode")) {
                    globals::DarkMode::enabled = j["Others"]["DarkMode"].value("Enabled", false);
                    globals::DarkMode::alpha = j["Others"]["DarkMode"].value("Alpha", 50.0f);
                }

                if (j["Others"].contains("Projectile")) {
                    globals::Projectile::enabled = j["Others"]["Projectile"].value("Enabled", false);
                    ParseColor(j["Others"]["Projectile"]["Color"], globals::Projectile::Color);
                    globals::Projectile::line = j["Others"]["Projectile"].value("Line", false);
                    globals::Projectile::name = j["Others"]["Projectile"].value("Name", false);
                    globals::Projectile::box = j["Others"]["Projectile"].value("Box", false);
                    globals::Projectile::erase = j["Others"]["Projectile"].value("Erase", false);
                }

                if (j["Others"].contains("Smoke")) {
                    globals::Smoke::enabled = j["Others"]["Smoke"].value("Enabled", false);

                    if (j["Others"]["Smoke"].contains("Name")) {
                        globals::Smoke::name::enabled = j["Others"]["Smoke"]["Name"].value("Enabled", false);
                        ParseColor(j["Others"]["Smoke"]["Name"]["Color"], globals::Smoke::name::Color);
                    }

                    if (j["Others"]["Smoke"].contains("Circle")) {
                        globals::Smoke::circle::enabled = j["Others"]["Smoke"]["Circle"].value("Enabled", false);
                        ParseColor(j["Others"]["Smoke"]["Circle"]["Color"], globals::Smoke::circle::Color);
                    }

                    if (j["Others"]["Smoke"].contains("Countdown")) {
                        globals::Smoke::countdown::enabled = j["Others"]["Smoke"]["Countdown"].value("Enabled", false);

                        if (j["Others"]["Smoke"]["Countdown"].contains("Bar")) {
                            globals::Smoke::countdown::bar::enabled = j["Others"]["Smoke"]["Countdown"]["Bar"].value("Enabled", false);
                            ParseColor(j["Others"]["Smoke"]["Countdown"]["Bar"]["Color"], globals::Smoke::countdown::bar::Color);
                        }

                        if (j["Others"]["Smoke"]["Countdown"].contains("Text")) {
                            globals::Smoke::countdown::text::enabled = j["Others"]["Smoke"]["Countdown"]["Text"].value("Enabled", false);
                            ParseColor(j["Others"]["Smoke"]["Countdown"]["Text"]["Color"], globals::Smoke::countdown::text::Color);
                        }
                    }
                }
            }

            // Load Misc features
            if (j.contains("Misc")) {
                if (j["Misc"].contains("Hitmarker")) {
                    globals::Hitmarker::enabled = j["Misc"]["Hitmarker"].value("Enabled", false);
                    ParseColor(j["Misc"]["Hitmarker"]["Color"], globals::Hitmarker::Color);
                    globals::Hitmarker::thickness = j["Misc"]["Hitmarker"].value("Thickness", 2.0f);
                    globals::Hitmarker::length = j["Misc"]["Hitmarker"].value("Length", 6.0f);
                    globals::Hitmarker::gap = j["Misc"]["Hitmarker"].value("Gap", 3.0f);
                    globals::Hitmarker::duration = j["Misc"]["Hitmarker"].value("Duration", 25.0f);
                }

                if (j["Misc"].contains("Sound")) {
                    globals::Sound::enabled = j["Misc"]["Sound"].value("Enabled", false);
                    globals::Sound::volume = j["Misc"]["Sound"].value("Volume", 0.0f);

                    if (j["Misc"]["Sound"].contains("Hit")) {
                        globals::Hitsound::enabled = j["Misc"]["Sound"]["Hit"].value("Enabled", false);
                        globals::Hitsound::sound = j["Misc"]["Sound"]["Hit"].value("Sound", "yourfile.wav");
                    }

                    if (j["Misc"]["Sound"].contains("Kill")) {
                        globals::Killsound::enabled = j["Misc"]["Sound"]["Kill"].value("Enabled", false);
                        globals::Killsound::sound = j["Misc"]["Sound"]["Kill"].value("Sound", "yourfile.wav");

                        if (j["Misc"]["Sound"]["Kill"].contains("Hs")) {
                            globals::HsKillsound::enabled = j["Misc"]["Sound"]["Kill"]["Hs"].value("Enabled", false);
                            globals::HsKillsound::sound = j["Misc"]["Sound"]["Kill"]["Hs"].value("Sound", "yourfile.wav");
                        }
                    }
                }

                if (j["Misc"].contains("NoScope Crosshair")) {
                    globals::Crosshair::enabled = j["Misc"]["NoScope Crosshair"].value("Enabled", false);
                    ParseColor(j["Misc"]["NoScope Crosshair"]["Color"], globals::Crosshair::Color);
                    globals::Crosshair::thickness = j["Misc"]["NoScope Crosshair"].value("Thickness", 2.0f);
                    globals::Crosshair::length = j["Misc"]["NoScope Crosshair"].value("Length", 6.0f);
                    globals::Crosshair::gap = j["Misc"]["NoScope Crosshair"].value("Gap", 3.0f);

                    if (j["Misc"]["NoScope Crosshair"].contains("Dot")) {
                        globals::Crosshair::dotenabled = j["Misc"]["NoScope Crosshair"]["Dot"].value("Enabled", false);
                        globals::Crosshair::dotSize = j["Misc"]["NoScope Crosshair"]["Dot"].value("Size", 1.0f);
                    }
                }

                if (j["Misc"].contains("Keystrokes")) {
                    globals::Keystrokes::enabled = j["Misc"]["Keystrokes"].value("Enabled", false);
                    ParseColor(j["Misc"]["Keystrokes"]["Color1"], globals::Keystrokes::Color);
                    ParseColor(j["Misc"]["Keystrokes"]["Color2"], globals::Keystrokes::pressedColor);
                    globals::Keystrokes::scale = j["Misc"]["Keystrokes"].value("Scale", 1.0f);
                    globals::Keystrokes::posX = j["Misc"]["Keystrokes"].value("PosX", 50.0f);
                    globals::Keystrokes::posY = j["Misc"]["Keystrokes"].value("PosY", 200.0f);
                }

                if (j["Misc"].contains("RecoilCrosshair")) {
                    globals::Misc::RecoilCrosshair::enabled = j["Misc"]["RecoilCrosshair"].value("Enabled", false);
                    globals::Misc::RecoilCrosshair::size = j["Misc"]["RecoilCrosshair"].value("Size", 3.0f);
                    ParseColor(j["Misc"]["RecoilCrosshair"]["Color"], globals::Misc::RecoilCrosshair::Color);
                }

                if (j["Misc"].contains("Watermark")) {
                    globals::Watermark::enabled = j["Misc"]["Watermark"].value("Enabled", false);
                    ParseColor(j["Misc"]["Watermark"]["TextColor"], globals::Watermark::Color);
                    ParseColor(j["Misc"]["Watermark"]["GradientColor"], globals::Watermark::gradientColor);
                }
            }

            // Load Menu settings
            if (j.contains("Menu")) {
                ParseColor(j["Menu"]["Color"], globals::MenuOutline::Color);
            }

            // No auto-load settings to load

            std::cout << "Successfully loaded config from: " << fullPath << std::endl;
        }
        catch (const std::exception& e) {
            std::cerr << "Error reading config JSON: " << e.what() << std::endl;
            CreateDefaultConfig();
        }

        file.close();
    }

    void CreateDefaultConfig() {
        std::string configPath = GetConfigPath();
        EnsureConfigDirectories();

        // Set default values for all settings
        globals::Triggerbot::enabled = false;
        globals::Triggerbot::hotkey = VK_LMENU;

        globals::Legitbot::enabled = true;
        globals::Legitbot::teamcheck = true;
        globals::Legitbot::visiblecheck = true;
        globals::Legitbot::smoothness = 1.0f;
        globals::Legitbot::radius = 99.9f;
        globals::Legitbot::Circle::enabled = true;
        globals::Legitbot::Circle::filled = true;
        globals::Legitbot::Circle::Color = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);

        globals::Esp::enabled = false;
        globals::Esp::ignoreTeammates = true;

        // Set default ESP values (using existing globals::Esp structure)
        globals::Esp::Box::enabled = false;
        globals::Esp::Box::Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
        globals::Esp::Box::type = 0;
        globals::Esp::Box::rounding = 0.0f;
        globals::Esp::Box::length = 0.5f;
        globals::Esp::Box::outline = false;
        globals::Esp::Box::outlineColor = ImVec4(0.0f, 0.0f, 0.0f, 1.0f);
        globals::Esp::Box::Spotted::enabled = false;
        globals::Esp::Box::Spotted::Color = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
        globals::Esp::Box::Filled::enabled = false;
        globals::Esp::Box::Filled::Color = ImVec4(1.0f, 1.0f, 1.0f, 0.25f);
        globals::Esp::Box::Filled::Color2 = ImVec4(1.0f, 1.0f, 1.0f, 0.25f);

        globals::Esp::Health::Bar::enabled = false;
        globals::Esp::Health::Bar::Style::type = 0; // Reactive
        globals::Esp::Health::Bar::Color = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
        globals::Esp::Health::Bar::Glow::enabled = true;
        globals::Esp::Health::Value::enabled = true;
        globals::Esp::Health::Value::Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);

        globals::Esp::Armor::Bar::enabled = false;
        globals::Esp::Armor::Bar::Color = ImVec4(0.3f, 0.6f, 1.0f, 1.0f); // Medium blue
        globals::Esp::Armor::Bar::Glow::enabled = true;
        globals::Esp::Armor::Value::enabled = true;
        globals::Esp::Armor::Value::Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);

        globals::Esp::Skeleton::enabled = false;
        globals::Esp::Skeleton::Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
        globals::Esp::Skeleton::Spotted::enabled = false;
        globals::Esp::Skeleton::Spotted::Color = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
        globals::Esp::Skeleton::Head::enabled = false;
        globals::Esp::Skeleton::Head::Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
        globals::Esp::Skeleton::Dots::enabled = false;
        globals::Esp::Skeleton::Dots::Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
        globals::Esp::Skeleton::Dots::radius = 0.2f;

        globals::Esp::Snapline::enabled = false;
        globals::Esp::Snapline::Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
        globals::Esp::Snapline::Spotted::enabled = false;
        globals::Esp::Snapline::Spotted::Color = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
        globals::Esp::Snapline::thickness = 1.0f;
        globals::Esp::Snapline::Start = "UPPER";
        globals::Esp::Snapline::End::upper = true;
        globals::Esp::Snapline::End::head = false;
        globals::Esp::Snapline::End::center = false;
        globals::Esp::Snapline::End::lower = false;

        globals::Esp::Info::enabled = false;
        globals::Esp::Info::Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
        globals::Esp::Info::Spotted::enabled = false;
        globals::Esp::Info::Spotted::Color = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
        globals::Esp::Info::Name::player = false;
        globals::Esp::Info::Name::weapon = false;
        globals::Esp::Info::Icon::enabled = false;
        globals::Esp::Info::state = false;

        globals::Esp::Viewline::enabled = false;
        globals::Esp::Viewline::Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
        globals::Esp::Viewline::Spotted::enabled = false;
        globals::Esp::Viewline::Spotted::Color = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
        globals::Esp::Viewline::Dot::enabled = false;
        globals::Esp::Viewline::Dot::Color = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
        globals::Esp::Viewline::length = 10.0f;

        globals::Esp::Death::enabled = false;
        globals::Esp::Death::duration = 1.0f;
        globals::Esp::Death::Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
        globals::Esp::Death::Box::enabled = false;
        globals::Esp::Death::Skeleton::enabled = false;
        globals::Esp::Death::Info::enabled = false;
        globals::Esp::Death::Health::enabled = false;
        globals::Esp::Death::Armor::enabled = false;
        globals::Esp::Death::Snapline::enabled = false;

        // Misc settings
        globals::Keystrokes::enabled = false;
        globals::Keystrokes::Color = ImVec4(1.0f, 1.0f, 1.0f, 0.25f);
        globals::Keystrokes::pressedColor = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
        globals::Keystrokes::scale = 1.0f;
        globals::Keystrokes::posX = 50.0f;
        globals::Keystrokes::posY = 200.0f;

        globals::Misc::RecoilCrosshair::enabled = false;
        globals::Misc::RecoilCrosshair::size = 3.0f;
        globals::Misc::RecoilCrosshair::Color = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);

        globals::Watermark::enabled = false;
        globals::Watermark::Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
        globals::Watermark::gradientColor = ImVec4(0.3f, 0.6f, 1.0f, 1.0f);

        // Save the default config
        SaveConfig(configPath, "Nebula.json");
        std::cout << "Created default configuration" << std::endl;
    }

    bool ValidateConfig(const json& config) {
        // Basic validation - check if required sections exist
        if (!config.contains("ESP") || !config.contains("Enemy")) {
            return false;
        }
        return true;
    }

    void CreateBackup(const std::string& configPath) {
        try {
            std::string backupPath = configPath + ".backup";
            fs::copy_file(configPath, backupPath, fs::copy_options::overwrite_existing);
            std::cout << "Config backup created: " << backupPath << std::endl;
        } catch (const fs::filesystem_error& e) {
            std::cerr << "Failed to create backup: " << e.what() << std::endl;
        }
    }

    bool RestoreFromBackup(const std::string& configPath) {
        try {
            std::string backupPath = configPath + ".backup";
            if (fs::exists(backupPath)) {
                fs::copy_file(backupPath, configPath, fs::copy_options::overwrite_existing);
                std::cout << "Config restored from backup" << std::endl;
                return true;
            }
        } catch (const fs::filesystem_error& e) {
            std::cerr << "Failed to restore from backup: " << e.what() << std::endl;
        }
        return false;
    }

    // Auto-save functionality (placeholder for future implementation)
    void EnableAutoSave(bool enabled, float intervalSeconds) {
        // TODO: Implement auto-save timer functionality
        std::cout << "Auto-save " << (enabled ? "enabled" : "disabled")
                  << " with interval: " << intervalSeconds << "s" << std::endl;
    }

    void DisableAutoSave() {
        EnableAutoSave(false, 0.0f);
    }

    // No auto-load functionality needed

    std::vector<std::string> GetAvailableConfigs() {
        std::vector<std::string> configs;
        std::string configPath = GetConfigPath();

        try {
            for (const auto& entry : fs::directory_iterator(configPath)) {
                if (entry.is_regular_file() && entry.path().extension() == ".json") {
                    configs.push_back(entry.path().filename().string());
                }
            }
        } catch (const fs::filesystem_error& e) {
            std::cerr << "Error reading config directory: " << e.what() << std::endl;
        }

        // Ensure default config exists in list
        if (std::find(configs.begin(), configs.end(), "Nebula.json") == configs.end()) {
            configs.push_back("Nebula.json");
        }

        std::sort(configs.begin(), configs.end());
        return configs;
    }

    bool CreateNewConfig(const std::string& configName) {
        if (configName.empty()) return false;

        std::string fullName = configName;
        if (fullName.find(".json") == std::string::npos) {
            fullName += ".json";
        }

        std::string configPath = GetConfigPath();
        std::string fullPath = configPath + fullName;

        // Check if file already exists
        if (fs::exists(fullPath)) {
            std::cerr << "Config file already exists: " << fullName << std::endl;
            return false;
        }

        // Save current settings to new config
        SaveConfig(configPath, fullName);
        return true;
    }

    bool DeleteConfig(const std::string& configName) {
        if (configName.empty() || configName == "Nebula.json") {
            return false; // Don't allow deleting default config
        }

        std::string configPath = GetConfigPath();
        std::string fullPath = configPath + configName;

        try {
            if (fs::exists(fullPath)) {
                fs::remove(fullPath);
                std::cout << "Deleted config: " << configName << std::endl;
                return true;
            }
        } catch (const fs::filesystem_error& e) {
            std::cerr << "Error deleting config: " << e.what() << std::endl;
        }

        return false;
    }

    bool GetAutoLoadEnabled() {
        return false; // Auto-load disabled
    }

    void SetAutoLoadEnabled(bool enabled) {
        // Auto-load functionality removed
    }

    static std::string selectedConfig = "Nebula.json";

    std::string GetSelectedConfig() {
        return selectedConfig;
    }

    void SetSelectedConfig(const std::string& configName) {
        selectedConfig = configName;
    }
}

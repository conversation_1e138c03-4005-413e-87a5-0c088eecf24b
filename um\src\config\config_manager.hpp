#pragma once
#include "pch.h"
#include <string>
#include <fstream>
#include <filesystem>
#include <iomanip>
#include <map>
#include "../imgui/imgui.h"
#include "nlohmann/json.hpp"

using json = nlohmann::json;

namespace ConfigManager {

    // Color conversion utilities
    void ParseColor(const json& colorJson, ImVec4& color);
    json ColorToJson(const ImVec4& color);

    // Main config functions
    void SaveConfig(const std::string& path, const std::string& filename);
    void LoadConfig(const std::string& path, const std::string& filename);

    // Path utilities
    std::string GetConfigPath();
    void EnsureConfigDirectories();

    // Default config creation
    void CreateDefaultConfig();

    // Config validation
    bool ValidateConfig(const json& config);

    // Auto-save functionality
    void EnableAutoSave(bool enabled = true, float intervalSeconds = 30.0f);
    void DisableAutoSave();

    // Config backup
    void CreateBackup(const std::string& configPath);
    bool RestoreFromBackup(const std::string& configPath);

    // Multiple config profiles
    std::vector<std::string> GetAvailableConfigs();
    bool CreateNewConfig(const std::string& configName);
    bool DeleteConfig(const std::string& configName);

    // Auto-load settings
    bool GetAutoLoadEnabled();
    void SetAutoLoadEnabled(bool enabled);
    std::string GetSelectedConfig();
    void SetSelectedConfig(const std::string& configName);
}

#pragma once

#include <string>
#include <unordered_map>
#include "../src/cheat/animations/ui/menu_animations.hpp"

static ImVec4 copiedColor;
static bool   hasColorCopied = false;

// Helper function to create a slightly lighter color
ImVec4 LightenColor( const ImVec4& color, float factor = 0.2f ) {
  return ImVec4(
      ImMin( color.x + factor, 1.0f ),
      ImMin( color.y + factor, 1.0f ),
      ImMin( color.z + factor, 1.0f ),
      color.w );
}

// Function declarations (kept for backward compatibility)
bool CustomCheckbox( const char* label, bool* v );

// Helper function for easy replacement of ImGui::Checkbox calls
inline bool AnimatedCheckbox( const char* label, bool* v ) {
  return CustomCheckbox( label, v );
}



void CustomColorPicker( const char* label, ImVec4& col, bool useAlpha, float Width ) {
  static char hexValue[9];
  sprintf_s( hexValue, sizeof( hexValue ), "%02X%02X%02X%02X",
             static_cast<int>( col.x * 255.0f ),
             static_cast<int>( col.y * 255.0f ),
             static_cast<int>( col.z * 255.0f ),
             static_cast<int>( col.w * 255.0f ) );

  ImGui::PushItemWidth( Width );  // Begrenzt die Breite der Widgets

  // Store previous color for animation
  static std::unordered_map<std::string, ImVec4> previousPickerColors;
  std::string pickerId = std::string( label ) + "_picker";

  // Store the color before picker interaction
  ImVec4 colorBeforeChange = col;

  bool colorChanged = useAlpha
                          ? ImGui::ColorPicker4( "##picker", (float*)&col,
                                                 ImGuiColorEditFlags_AlphaBar | ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_NoSidePreview )
                          : ImGui::ColorPicker3( "##picker", (float*)&col,
                                                 ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_NoSidePreview );

  if ( colorChanged ) {
    // Register color selection animation from previous to new color
    if ( previousPickerColors.find( pickerId ) != previousPickerColors.end() ) {
      MenuAnimations::RegisterColorSelectionTransition( pickerId, colorBeforeChange, col, 0.3f );
    }
    previousPickerColors[pickerId] = col;

    sprintf_s( hexValue, sizeof( hexValue ), "%02X%02X%02X%02X",
               static_cast<int>( col.x * 255.0f ),
               static_cast<int>( col.y * 255.0f ),
               static_cast<int>( col.z * 255.0f ),
               static_cast<int>( col.w * 255.0f ) );
  }

  // Apply color selection animation if active
  col = MenuAnimations::GetColorSelectionValue( pickerId, col );
  ImGui::InputText( "##hex", hexValue, sizeof( hexValue ),
                    ImGuiInputTextFlags_CharsHexadecimal | ImGuiInputTextFlags_CharsUppercase );

  if ( strlen( hexValue ) == 8 ) {
    unsigned int r, g, b, a;
    if ( sscanf_s( hexValue, "%02X%02X%02X%02X", &r, &g, &b, &a ) == 4 ) {
      col.x = static_cast<float>( r ) / 255.0f;
      col.y = static_cast<float>( g ) / 255.0f;
      col.z = static_cast<float>( b ) / 255.0f;
      col.w = static_cast<float>( a ) / 255.0f;
    }
  }
  ImGui::PopItemWidth();
}

void CustomColorPicker4( const char* label, ImVec4& col, float Width ) {
  CustomColorPicker( label, col, true, Width );
}

void CustomColorPicker3( const char* label, ImVec4& col, float Width ) {
  CustomColorPicker( label, col, false, Width );
}

void CustomColorEdit( const char* label, ImVec4& col, bool useAlpha ) {
  std::string popupLabel       = std::string( label ) + "##popup";
  std::string contextMenuLabel = std::string( label ) + "##contextmenu";
  std::string popupId          = std::string( label );

  ImVec2 buttonSize = ImGui::CalcItemSize( ImVec2( 30.f, 10.f ), 0, 0 );
  buttonSize.x += ImGui::GetStyle().FramePadding.x * 2;
  buttonSize.y += ImGui::GetStyle().FramePadding.y * 2;

  // Store previous color for animation
  static std::unordered_map<std::string, ImVec4> previousColors;
  static std::unordered_map<std::string, bool> popupJustOpened;

  if ( ImGui::ColorButton( label, col, ImGuiColorEditFlags_NoPicker | ImGuiColorEditFlags_NoTooltip ) ) {
    ImGui::OpenPopup( popupLabel.c_str() );
    // Register popup opening animation
    MenuAnimations::RegisterColorPickerPopupOpen( popupId, 0.25f );
    popupJustOpened[popupId] = true;
    previousColors[popupId] = col;
  }

  if ( ImGui::BeginPopupContextItem( contextMenuLabel.c_str() ) ) {
    ImGui::PushStyleVar( ImGuiStyleVar_ItemSpacing, ImVec2( 0, 1 ) );
    ImGui::PushStyleColor( ImGuiCol_Button, LightenColor( ImGui::GetStyle().Colors[ImGuiCol_Button] ) );
    ImGui::PushStyleColor( ImGuiCol_ButtonHovered, LightenColor( ImGui::GetStyle().Colors[ImGuiCol_ButtonHovered] ) );
    ImGui::PushStyleColor( ImGuiCol_ButtonActive, LightenColor( ImGui::GetStyle().Colors[ImGuiCol_ButtonActive], 0.4f ) );

    if ( ImGui::Button( "Copy", ImVec2( buttonSize.x, 0 ) ) ) {
      copiedColor    = col;
      hasColorCopied = true;
      ImGui::CloseCurrentPopup();
    }
    if ( ImGui::Button( "Paste", ImVec2( buttonSize.x, 0 ) ) ) {
      if ( hasColorCopied ) {
        col = copiedColor;
      }
      ImGui::CloseCurrentPopup();
    }

    ImGui::PopStyleColor( 3 );
    ImGui::PopStyleVar();
    ImGui::EndPopup();
  }

  if ( ImGui::BeginPopup( popupLabel.c_str() ) ) {
    // Apply popup fade animation
    float popupAlpha = MenuAnimations::GetColorPickerPopupAlpha( popupId );
    ImGui::PushStyleVar( ImGuiStyleVar_Alpha, popupAlpha );

    static char hexValue[9];
    sprintf_s( hexValue, sizeof( hexValue ), "%02X%02X%02X%02X",
               static_cast<int>( col.x * 255.0f ),
               static_cast<int>( col.y * 255.0f ),
               static_cast<int>( col.z * 255.0f ),
               static_cast<int>( col.w * 255.0f ) );

    // Store the color before picker interaction
    ImVec4 colorBeforeChange = col;

    bool colorChanged = useAlpha
                            ? ImGui::ColorPicker4( "##picker", (float*)&col, ImGuiColorEditFlags_AlphaBar | ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_NoSidePreview )
                            : ImGui::ColorPicker3( "##picker", (float*)&col, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_NoSidePreview );

    if ( colorChanged ) {
      // Register color selection animation from previous to new color
      if ( previousColors.find( popupId ) != previousColors.end() ) {
        MenuAnimations::RegisterColorSelectionTransition( popupId, colorBeforeChange, col, 0.3f );
      }

      sprintf_s( hexValue, sizeof( hexValue ), "%02X%02X%02X%02X",
                 static_cast<int>( col.x * 255.0f ),
                 static_cast<int>( col.y * 255.0f ),
                 static_cast<int>( col.z * 255.0f ),
                 static_cast<int>( col.w * 255.0f ) );
    }

    // Apply color selection animation if active
    col = MenuAnimations::GetColorSelectionValue( popupId, col );

    ImGui::Dummy( ImVec2( 5, 5 ) );

    ImGui::InputText( "##hex", hexValue, sizeof( hexValue ), ImGuiInputTextFlags_CharsHexadecimal | ImGuiInputTextFlags_CharsUppercase );

    if ( strlen( hexValue ) == 8 ) {
      unsigned int r, g, b, a;
      if ( sscanf_s( hexValue, "%02X%02X%02X%02X", &r, &g, &b, &a ) == 4 ) {
        col.x = static_cast<float>( r ) / 255.0f;
        col.y = static_cast<float>( g ) / 255.0f;
        col.z = static_cast<float>( b ) / 255.0f;
        col.w = static_cast<float>( a ) / 255.0f;
      }
    }

    ImGui::Dummy( ImVec2( 5, 5 ) );
    ImGui::Separator();
    ImGui::Dummy( ImVec2( 5, 5 ) );

    if ( ImGui::Button( "OK", ImVec2( -1, 0 ) ) ) {
      // Register popup closing animation
      MenuAnimations::RegisterColorPickerPopupClose( popupId, 0.2f );
      ImGui::CloseCurrentPopup();
      popupJustOpened[popupId] = false;
    }

    // Pop the alpha style variable
    ImGui::PopStyleVar();
    ImGui::EndPopup();
  } else {
    // Reset popup state when popup is closed
    if ( popupJustOpened.find( popupId ) != popupJustOpened.end() && popupJustOpened[popupId] ) {
      popupJustOpened[popupId] = false;
    }
  }
}

void CustomColorEdit4( const char* label, ImVec4& col ) {
  CustomColorEdit( label, col, true );
}

void CustomColorEdit3( const char* label, ImVec4& col ) {
  CustomColorEdit( label, col, false );
}

// Custom animated checkbox function (now just calls the standard ImGui::Checkbox which has animations built-in)
bool CustomCheckbox( const char* label, bool* v ) {
  // Since we've modified the standard ImGui::Checkbox to include animations,
  // this function now just calls the standard checkbox
  return ImGui::Checkbox( label, v );
}



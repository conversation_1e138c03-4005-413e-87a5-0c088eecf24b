#include "pch.h" // Make sure this includes <algorithm> for std::min/max or undefines macros
#include "visuals.hpp"
#include "../../../ItemIndex.hpp"
#include "../../globals.hpp"
#include "../../../utils/utils.hpp" // For RemoveSuffix and HorizontalCircle
#include "../../gamedata.hpp" // GameData convenience wrapper system
#include "../../../window/window.hpp" // For espfont and gunIcons

#include <algorithm> // For std::min and std::max
#include <cmath>     // For M_PI, cosf, sinf (ensure _USE_MATH_DEFINES is defined if <PERSON>_<PERSON><PERSON> is missing)
#include <unordered_map>
#include <unordered_set>

using namespace globals;

// Temporary structure for tracking player data (for death detection) - RESTORED
struct DeathPlayerData {
    Vector worldPosition;
    Vector eyeWorldPosition;
    uint64_t boneArray;
    std::string playerName;
    uint32_t playerFlags;
    uint16_t weaponIndex;
    int health;
    int armor;
    int team;
    bool wasSpotted;

    DeathPlayerData() = default;

    DeathPlayerData(const Vector& pos, const Vector& eyePos, uint64_t bones, const std::string& name,
                   uint32_t flags, uint16_t weapon, int hp, int ar, int tm, bool spotted)
        : worldPosition(pos), eyeWorldPosition(eyePos), boneArray(bones), playerName(name),
          playerFlags(flags), weaponIndex(weapon), health(hp), armor(ar),
          team(tm), wasSpotted(spotted) {}
};

void VISUALS::RenderESP( HANDLE driverHandle, const Reader& reader ) {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  auto playerList = reader.getPlayerListCopy();
  auto entityList = reader.getEntityListCopy();

  ImGui::GetStyle().AntiAliasedLines = false;
  ImGui::GetStyle().AntiAliasedFill = false;
  ImGui::GetStyle().AntiAliasedLinesUseTex = false;

  if ( DarkMode::enabled || DarkMode::enabled && overlayVisible ) {
    VISUALS::Darkmode();
  }

  // CLEAN: Use GameData convenience wrapper system
  int localTeam = GameData::getLocalTeam();

  // Track current alive players and register deaths
  static std::unordered_map<int, DeathPlayerData> lastKnownPlayerData;
  std::unordered_set<int> currentlyAlive;

  // First pass: collect currently alive players and update their data
  for ( const auto& player : playerList ) {
    currentlyAlive.insert(player.entityId);

    // CLEAN: Use GameData for player position
    Vector playerWorldOrigin = GameData::getPlayerPosition(player.pCSPlayerPawn);
    // Note: 0xCB0 offset might need to be added to GameData in the future
    Vector playerViewOffset = driver::read_memory<Vector>(driverHandle, player.pCSPlayerPawn + 0xCB0);
    Vector playerEyeWorldPos = playerWorldOrigin;
    playerEyeWorldPos.z += playerViewOffset.z;

    lastKnownPlayerData[player.entityId] = DeathPlayerData(
      playerWorldOrigin, playerEyeWorldPos, player.BoneArray, player.PlayerName,
      player.PlayerFlags, player.ItemDefinitionIndex,
      player.health, player.armor, player.team, player.PlayerSpotted
    );
  }

  // Check for players that are no longer alive and register deaths
  if (globals::Esp::Death::enabled) {
    for (auto it = lastKnownPlayerData.begin(); it != lastKnownPlayerData.end();) {
      if (currentlyAlive.find(it->first) == currentlyAlive.end()) {
        // Player is no longer in the alive list - register death using new system
        DeathAnimations::RegisterPlayerDeath
        (
          it->first,
          it->second.worldPosition,
          it->second.eyeWorldPosition,
          it->second.boneArray,
          it->second.playerName,
          it->second.playerFlags,
          it->second.weaponIndex,
          it->second.health,
          it->second.armor,
          it->second.team,
          it->second.wasSpotted
        );

        it = lastKnownPlayerData.erase(it);
      } else {
        ++it;
      }
    }
  }

  for ( const auto& player : playerList ) {
    // Check if player is enemy or teammate
    const bool isCurrentPlayerEnemy = (player.team != localTeam);

    // Skip teammates if "Ignore Teammates" is enabled
    if (!isCurrentPlayerEnemy && Esp::ignoreTeammates) continue;

    // Set spotted drawing colors based on player spotted state
    bool spotted = player.PlayerSpotted;
    Esp::Box::Spotted::drawingColor      = ( spotted && Esp::Box::Spotted::enabled ) ? Esp::Box::Spotted::Color : Esp::Box::Color;
    Esp::Skeleton::Spotted::drawingColor = ( spotted && Esp::Skeleton::Spotted::enabled ) ? Esp::Skeleton::Spotted::Color : Esp::Skeleton::Color;
    Esp::Snapline::Spotted::drawingColor = ( spotted && Esp::Snapline::Spotted::enabled ) ? Esp::Snapline::Spotted::Color : Esp::Snapline::Color;
    Esp::Info::Spotted::drawingColor     = ( spotted && Esp::Info::Spotted::enabled ) ? Esp::Info::Spotted::Color : Esp::Info::Color;
    Esp::Viewline::Spotted::drawingColor = ( spotted && Esp::Viewline::Spotted::enabled ) ? Esp::Viewline::Spotted::Color : Esp::Viewline::Color;

    // CLEAN: Use GameData for player position
    Vector playerWorldOrigin = GameData::getPlayerPosition(player.pCSPlayerPawn);
    // Note: 0xCB0 offset might need to be added to GameData in the future
    Vector playerViewOffset = driver::read_memory<Vector>(driverHandle, player.pCSPlayerPawn + 0xCB0);
    Vector playerWorldHeadPos = playerWorldOrigin;
    playerWorldHeadPos.z += playerViewOffset.z + 9;

    // CLEAN: Get viewMatrix once per frame via GameData
    view_matrix_t viewMatrix = GameData::getViewMatrix();

    Vector screenHeadPos, screenFeetPos;
    if ( Vector::world_to_screen( viewMatrix, playerWorldHeadPos, screenHeadPos ) &&
      Vector::world_to_screen( viewMatrix, playerWorldOrigin, screenFeetPos ) ) {

      const float boxHeight = screenFeetPos.y - screenHeadPos.y;
      const float boxHalfWidth  = boxHeight * 0.25f;

      // Always render snaplines regardless of culling
      if (Esp::Snapline::enabled) {
        DrawPlayerSnapline( screenHeadPos, screenFeetPos );
      }

      // Only render other ESP elements if they are completely on screen

      if (Esp::Health::Bar::enabled) {
        // Register health animation automatically - now with proper value change detection
        HealthAnimations::UpdateHealthAnimation(player.entityId, static_cast<float>(player.health));
        DrawPlayerHealth( screenHeadPos, boxHeight, boxHalfWidth, player.health, player.entityId );
      }

      if (Esp::Armor::Bar::enabled) {
        // Register armor animation automatically - now with proper value change detection
        ArmorAnimations::UpdateArmorAnimation(player.entityId, static_cast<float>(player.armor));
        DrawPlayerArmor( screenHeadPos, boxHeight, boxHalfWidth, player.armor, player.entityId );
      }

      if (Esp::Box::enabled) {
        if (Esp::Box::type == 0) {
          DrawPlayerBox( screenHeadPos, screenFeetPos );
        } else {
          DrawPlayerCorneredBox( screenHeadPos, screenFeetPos );
        }
      }

      if (Esp::Box::Filled::enabled) {
        DrawPlayerFilledBox( screenHeadPos, screenFeetPos );
      }

      if (Esp::Info::enabled) {
        DrawPlayerInfo( screenHeadPos, screenFeetPos, player.PlayerName, player.PlayerFlags, player.ItemDefinitionIndex );
      }

      if (Esp::Viewline::enabled) {
        // Note: angEyeAngles offset might need to be added to GameData in the future
        Vector playerEyeAngles = driver::read_memory<Vector>( driverHandle, player.pCSPlayerPawn + Offset::Pawn::angEyeAngles );
        Vector playerEyeWorldPos = playerWorldOrigin;
        playerEyeWorldPos.z += playerViewOffset.z;
        DrawViewline( playerEyeAngles, playerEyeWorldPos );
      }

      if (Esp::Skeleton::enabled) {
        DrawPlayerSkeleton( player.BoneArray );

        if (Esp::Skeleton::Dots::enabled) {
          DrawPlayerJoints( player.BoneArray );
        }

        if (Esp::Skeleton::Head::enabled) {
          DrawPlayerHead( player.BoneArray );
        }
      }
    }
  }

  // Render death animations using the new clean system
  DeathAnimations::RenderAll(GameData::getViewMatrix(), localTeam);

  // Static tracking for projectile positions to detect static projectiles
  static std::unordered_map<uintptr_t, std::pair<Vector, int>> projectilePositionTracker;
  static std::unordered_set<uintptr_t> bannedProjectiles; // Permanently banned projectiles
  static std::unordered_map<uintptr_t, std::vector<Vector>> projectileTrajectories; // Store all positions
  static std::unordered_map<uintptr_t, std::chrono::steady_clock::time_point> trajectoryStartTime; // When trajectory removal starts
  static const int MAX_STATIC_COUNT = 100;

  // Render projectiles and grenades
  for (const auto& entity : entityList) {
    if ( entity.className.find( "_projectile" ) == std::string::npos)
      continue;

    // Check if this projectile is permanently banned
    if (bannedProjectiles.find(entity.BaseEntity) != bannedProjectiles.end()) {
      continue; // Skip banned projectiles forever
    }

    std::string cleanProjectileName = RemoveSuffix( entity.className, "_projectile" );

    // CLEAN: Use GameData for entity position
    Vector projectileOrigin = GameData::getEntityPosition(entity.BaseEntity);

    if ( projectileOrigin.isInvalid() )
      continue;

    // Check if this projectile should be rendered based on static detection
    auto& tracker = projectilePositionTracker[entity.BaseEntity];
    auto& trajectory = projectileTrajectories[entity.BaseEntity];

    // Check if projectile is at same position (simple comparison)
    bool samePosition = (tracker.first.x == projectileOrigin.x &&
                        tracker.first.y == projectileOrigin.y &&
                        tracker.first.z == projectileOrigin.z);

    if (samePosition) {
      // Projectile is at same position, increment counter
      tracker.second++;

      // If projectile has been static for too long, ban it permanently
      if (tracker.second >= MAX_STATIC_COUNT) {
        bannedProjectiles.insert(entity.BaseEntity);
        projectilePositionTracker.erase(entity.BaseEntity); // Remove from tracker

        // Handle trajectory removal based on line rendering setting
        if (globals::Projectile::line && globals::Projectile::erase) {
          // Start trajectory removal timer for smooth erase animation
          trajectoryStartTime[entity.BaseEntity] = std::chrono::steady_clock::now();
        } else {
          // Instantly remove trajectory when line rendering is disabled
          auto trajectoryIt = projectileTrajectories.find(entity.BaseEntity);
          if (trajectoryIt != projectileTrajectories.end()) {
            trajectoryIt->second.clear();
          }
        }

        continue; // Skip rendering this frame and forever
      }
    } else {
      // Projectile moved, reset counter and update position
      tracker.first = projectileOrigin;
      tracker.second = 0;

      // Add new position to trajectory
      trajectory.push_back(projectileOrigin);
    }

    // Regular projectile rendering (name and box)
    Vector projectileScreenPos;
    view_matrix_t viewMatrix = GameData::getViewMatrix();
    if (Vector::world_to_screen(viewMatrix, projectileOrigin, projectileScreenPos)) {
      // Only call DrawProjectile if name or box is enabled
      if (globals::Projectile::name || globals::Projectile::box) {
        DrawProjectile(projectileScreenPos, cleanProjectileName);
      }
    }
  }

  // Render trajectory lines for all projectiles (active and removed) - only if line rendering is enabled
  if (globals::Projectile::line) {
    view_matrix_t viewMatrix = GameData::getViewMatrix();
    ImVec4 trajectoryColor = ImVec4(1.0f, 1.0f, 1.0f, 1.0f); // White dots

    for (auto trajectoryIt = projectileTrajectories.begin(); trajectoryIt != projectileTrajectories.end(); ++trajectoryIt) {
      uintptr_t entityId = trajectoryIt->first;
      auto& trajectory = trajectoryIt->second;

      // Check if this trajectory should be smoothly removed (only when erase is enabled)
      auto startTimeIt = trajectoryStartTime.find(entityId);
      if (startTimeIt != trajectoryStartTime.end() && globals::Projectile::erase) {
        // Calculate how many points to remove based on time elapsed
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - startTimeIt->second).count();

        // Remove points gradually - one point every 500ms (static timing like before)
        int pointsToRemove = static_cast<int>(elapsed / 500);
        if (pointsToRemove > 0 && !trajectory.empty()) {
          int trajectorySize = static_cast<int>(trajectory.size());
          int actualRemove = (pointsToRemove < trajectorySize) ? pointsToRemove : trajectorySize;
          trajectory.erase(trajectory.begin(), trajectory.begin() + actualRemove);

          // If all points removed, clean up completely
          if (trajectory.empty()) {
            trajectoryStartTime.erase(startTimeIt);
            continue;
          }
        }
      }

      // Render trajectory as connected lines instead of dots
      if (trajectory.size() >= 2) {
        for (size_t i = 0; i < trajectory.size() - 1; ++i) {
          Vector worldPos1 = trajectory[i];     // Create mutable copy for world_to_screen
          Vector worldPos2 = trajectory[i + 1]; // Create mutable copy for world_to_screen
          Vector screenPos1, screenPos2;

          if (Vector::world_to_screen(viewMatrix, worldPos1, screenPos1) &&
              Vector::world_to_screen(viewMatrix, worldPos2, screenPos2)) {
            // Draw line between consecutive trajectory points
            Render::Line(screenPos1.x, screenPos1.y, screenPos2.x, screenPos2.y, globals::Projectile::Color, 1.f);
          }
        }
      }
    }
  } else {
    // If line rendering is disabled, instantly remove all trajectories that have started removal
    for (auto startTimeIt = trajectoryStartTime.begin(); startTimeIt != trajectoryStartTime.end();) {
      uintptr_t entityId = startTimeIt->first;
      auto trajectoryIt = projectileTrajectories.find(entityId);
      if (trajectoryIt != projectileTrajectories.end()) {
        trajectoryIt->second.clear(); // Instantly clear the trajectory
      }
      startTimeIt = trajectoryStartTime.erase(startTimeIt);
    }
  }

  // Clean up tracker for entities that no longer exist
  auto trackerIt = projectilePositionTracker.begin();
  while (trackerIt != projectilePositionTracker.end()) {
    bool entityExists = false;
    for (const auto& entity : entityList) {
      if (entity.BaseEntity == trackerIt->first) {
        entityExists = true;
        break;
      }
    }

    if (!entityExists) {
      // Entity disappeared, handle trajectory removal based on line rendering setting
      if (trajectoryStartTime.find(trackerIt->first) == trajectoryStartTime.end()) {
        if (globals::Projectile::line && globals::Projectile::erase) {
          // Start trajectory removal timer for smooth erase animation
          trajectoryStartTime[trackerIt->first] = std::chrono::steady_clock::now();
        } else {
          // Instantly remove trajectory when line rendering is disabled
          auto trajectoryIt = projectileTrajectories.find(trackerIt->first);
          if (trajectoryIt != projectileTrajectories.end()) {
            trajectoryIt->second.clear();
          }
        }
      }
      trackerIt = projectilePositionTracker.erase(trackerIt);
    } else {
      ++trackerIt;
    }
  }

  // Clean up empty trajectories
  auto trajectoryIt = projectileTrajectories.begin();
  while (trajectoryIt != projectileTrajectories.end()) {
    if (trajectoryIt->second.empty()) {
      trajectoryIt = projectileTrajectories.erase(trajectoryIt);
    } else {
      ++trajectoryIt;
    }
  }
}

void VISUALS::DrawPlayerBox(const Vector& screenHead, const Vector& screenFeet) {
  const float boxHeight = screenFeet.y - screenHead.y;
  const float boxHalfVisualWidth  = boxHeight * 0.25f;

  const float boxLeftEdgeX  = screenHead.x - boxHalfVisualWidth;
  const float boxTopEdgeY   = screenHead.y;
  const float boxTotalVisualWidth = boxHalfVisualWidth * 2.0f;

  const ImVec4 mainBoxColor   = Esp::Box::Spotted::drawingColor;
  ImVec4 outlineBoxColor = {0.f, 0.f, 0.f, 1.f};
  // Make outline fade with the main box color
  outlineBoxColor.w = mainBoxColor.w;

  const float outlineThicknessPx = 1.0f;

  if (Esp::Box::outline) {
    const float outerOutlineX = boxLeftEdgeX - outlineThicknessPx;
    const float outerOutlineY = boxTopEdgeY - outlineThicknessPx;
    const float outerOutlineWidth = boxTotalVisualWidth + (outlineThicknessPx * 2.0f);
    const float outerOutlineHeight = boxHeight + (outlineThicknessPx * 2.0f);
    Render::DrawRect(outerOutlineX, outerOutlineY, outerOutlineWidth, outerOutlineHeight, outlineBoxColor, Esp::Box::rounding, 1.f);

    const float innerOutlineX = boxLeftEdgeX + outlineThicknessPx;
    const float innerOutlineY = boxTopEdgeY + outlineThicknessPx;
    const float innerOutlineWidth = boxTotalVisualWidth - (outlineThicknessPx * 2.0f);
    const float innerOutlineHeight = boxHeight - (outlineThicknessPx * 2.0f);
    Render::DrawRect(innerOutlineX, innerOutlineY, innerOutlineWidth, innerOutlineHeight, outlineBoxColor, Esp::Box::rounding, 1.f);
  }

  Render::DrawRect(boxLeftEdgeX, boxTopEdgeY, boxTotalVisualWidth, boxHeight, mainBoxColor, Esp::Box::rounding, 1.f);
}

void VISUALS::DrawPlayerFilledBox(const Vector& screenHead, const Vector& screenFeet) {
  const float boxHeight = screenFeet.y - screenHead.y;
  const float boxHalfVisualWidth = boxHeight * 0.25f;

  const float boxLeftEdgeX  = screenHead.x - boxHalfVisualWidth;
  const float boxTopEdgeY   = screenHead.y;
  const float boxTotalVisualWidth = boxHalfVisualWidth * 2.0f;

  const ImVec4 topFillColor    = Esp::Box::Filled::Color;
  const ImVec4 bottomFillColor = Esp::Box::Filled::Color2;

  Render::DrawRectFilledMultiColor(
    boxLeftEdgeX,
    boxTopEdgeY,
    boxTotalVisualWidth,
    boxHeight,
    topFillColor,
    bottomFillColor
  );
}

void VISUALS::DrawPlayerCorneredBox(const Vector& screenHead, const Vector& screenFeet) {
  const float boxHeight           = screenFeet.y - screenHead.y;
  const float boxHalfVisualWidth  = boxHeight * 0.25f;
  const float cornerLineLength    = boxHalfVisualWidth * Esp::Box::length;

  const float leftEdgeX   = screenHead.x - boxHalfVisualWidth;
  const float rightEdgeX  = screenHead.x + boxHalfVisualWidth;
  const float topEdgeY    = screenHead.y;
  const float bottomEdgeY = screenHead.y + boxHeight;

  const ImVec4 mainCornerColor  = Esp::Box::Spotted::drawingColor;
  ImVec4 outlineCornerColor = {0.f, 0.f, 0.f, 1.f};
  // Make outline fade with the main corner color
  outlineCornerColor.w = mainCornerColor.w;

  const std::vector<std::pair<int, int>> fixedOutlineOffsets = {
    {-1, -1}, {1, -1}, {-1, 1}, {1, 1}
  };
  const float outlineLineThickness = 1.f;

  if (Esp::Box::outline) {
    for (const auto& offsetPair : fixedOutlineOffsets) {
      const float dx = static_cast<float>(offsetPair.first);
      const float dy = static_cast<float>(offsetPair.second);

      Render::Line(leftEdgeX + dx, topEdgeY + dy, leftEdgeX + cornerLineLength + dx, topEdgeY + dy, outlineCornerColor, outlineLineThickness);
      Render::Line(leftEdgeX + dx, topEdgeY + dy, leftEdgeX + dx, topEdgeY + cornerLineLength + dy, outlineCornerColor, outlineLineThickness);

      Render::Line(rightEdgeX + dx, topEdgeY + dy, rightEdgeX - cornerLineLength + dx, topEdgeY + dy, outlineCornerColor, outlineLineThickness);
      Render::Line(rightEdgeX + dx, topEdgeY + dy, rightEdgeX + dx, topEdgeY + cornerLineLength + dy, outlineCornerColor, outlineLineThickness);

      Render::Line(leftEdgeX + dx, bottomEdgeY + dy, leftEdgeX + cornerLineLength + dx, bottomEdgeY + dy, outlineCornerColor, outlineLineThickness);
      Render::Line(leftEdgeX + dx, bottomEdgeY + dy, leftEdgeX + dx, bottomEdgeY - cornerLineLength + dy, outlineCornerColor, outlineLineThickness);

      Render::Line(rightEdgeX + dx, bottomEdgeY + dy, rightEdgeX - cornerLineLength + dx, bottomEdgeY + dy, outlineCornerColor, outlineLineThickness);
      Render::Line(rightEdgeX + dx, bottomEdgeY + dy, rightEdgeX + dx, bottomEdgeY - cornerLineLength + dy, outlineCornerColor, outlineLineThickness);
    }
  }

  const float mainLineThickness = 1.f;
  Render::Line(leftEdgeX, topEdgeY, leftEdgeX + cornerLineLength, topEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(leftEdgeX, topEdgeY, leftEdgeX, topEdgeY + cornerLineLength, mainCornerColor, mainLineThickness);

  Render::Line(rightEdgeX, topEdgeY, rightEdgeX - cornerLineLength, topEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(rightEdgeX, topEdgeY, rightEdgeX, topEdgeY + cornerLineLength, mainCornerColor, mainLineThickness);

  Render::Line(leftEdgeX, bottomEdgeY, leftEdgeX + cornerLineLength, bottomEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(leftEdgeX, bottomEdgeY, leftEdgeX, bottomEdgeY - cornerLineLength, mainCornerColor, mainLineThickness);

  Render::Line(rightEdgeX, bottomEdgeY, rightEdgeX - cornerLineLength, bottomEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(rightEdgeX, bottomEdgeY, rightEdgeX, bottomEdgeY - cornerLineLength, mainCornerColor, mainLineThickness);

}

void VISUALS::DrawPlayerHealth(const Vector& topScreenPos, float playerBoxHeight, float playerBoxHalfWidth, int healthValue, int entityId) {
  if (Esp::Health::Bar::Style::type == Esp::HealthBarStyle::Solid) {
    DrawPlayerHealthBarSolid(topScreenPos, playerBoxHeight, playerBoxHalfWidth, healthValue, entityId);
  } else {
    DrawPlayerHealthBarReactive(topScreenPos, playerBoxHeight, playerBoxHalfWidth, healthValue, entityId);
  }
}

void VISUALS::DrawPlayerHealthBarReactive(const Vector& topScreenPos, float playerBoxHeight, float playerBoxHalfWidth, int healthValue, int entityId) {
  const float animatedHealth = HealthAnimations::GetAnimatedHealth(entityId, static_cast<float>(healthValue));
  // FIXED: Wrapped std::min and std::max in parentheses to avoid macro conflicts
  const float healthRatioClamped = (std::min)(1.f, (std::max)(0.f, animatedHealth / 100.f));

  const float barFullHeight = playerBoxHeight;
  const float filledBarActualHeight = barFullHeight * healthRatioClamped;
  const float barLeftOffset = 5.f;
  const float barPositionX = (topScreenPos.x - playerBoxHalfWidth) - barLeftOffset;
  const float barPositionY = topScreenPos.y;
  const float barVisualWidth = 2.f;
  const float backgroundVisualWidth = 4.f;
  const float backgroundBorderOffset = 1.f;

  const std::string healthValueText = std::to_string(static_cast<int>(animatedHealth));

  // For reactive health bar, check if this is a death animation using new AnimationManager
  const bool isDeathAnimation = AnimationManager::HasActiveDeathAnimation(entityId);

  ImVec4 currentBarReactiveColor;
  if (isDeathAnimation) {
    // Use death color with fade alpha for death animations
    currentBarReactiveColor = globals::Esp::Death::Color;
    currentBarReactiveColor.w *= AnimationManager::GetDeathFadeAlpha(entityId);
  } else {
    // Normal reactive color calculation
    currentBarReactiveColor = ImVec4{
      1.0f - healthRatioClamped,
      healthRatioClamped,
      0.0f,
      1.0f  // Normal reactive bar should always be fully opaque
    };
  }
  ImVec4 barBackgroundColor = {0.f, 0.f, 0.f, 1.f};
  // Make background fade with the health bar
  barBackgroundColor.w = currentBarReactiveColor.w;

  if (Esp::Health::Bar::Glow::enabled) {
    const ImVec4 glowEffectColor = {
      currentBarReactiveColor.x,
      currentBarReactiveColor.y,
      currentBarReactiveColor.z,
      0.7f
    };
    const int glowBlurRadius = 7;
    const int glowSpread = 2;
    Render::DrawRectGlow(
      barPositionX,
      barPositionY,
      barVisualWidth,
      barFullHeight,
      currentBarReactiveColor,
      glowEffectColor,
      glowBlurRadius,
      glowSpread
    );
  }

  const float bgRectX = barPositionX - backgroundBorderOffset;
  const float bgRectY = barPositionY - backgroundBorderOffset;
  const float bgRectWidth = backgroundVisualWidth;
  const float bgRectHeight = barFullHeight + (backgroundBorderOffset * 2.0f);
  Render::DrawRectFilled(bgRectX, bgRectY, bgRectWidth, bgRectHeight, barBackgroundColor, 0);

  const float filledBarRenderY = barPositionY + (barFullHeight - filledBarActualHeight);
  Render::DrawRectFilled(barPositionX, filledBarRenderY, barVisualWidth, filledBarActualHeight, currentBarReactiveColor, 0);

  if (Esp::Health::Value::enabled && animatedHealth < 100) {
    ImGui::PushFont(espfont);
    const float healthTextHorizontalOffset = (static_cast<int>(animatedHealth) <= 9) ? 3.f : 7.f;
    const float textRenderPosX = barPositionX - healthTextHorizontalOffset + 1.f;
    const float textRenderPosY = filledBarRenderY - 6.f;
    ImVec4 healthTextColor = Esp::Health::Value::Color;
    // Make health text fade but keep it white
    healthTextColor.w = currentBarReactiveColor.w;

    Render::Text(textRenderPosX, textRenderPosY, healthTextColor, healthValueText, fontSize, font_flags_t::outline);
    ImGui::PopFont();
  }
}

void VISUALS::DrawPlayerHealthBarSolid(const Vector& topScreenPos, float playerBoxHeight, float playerBoxHalfWidth, int healthValue, int entityId) {
  const float animatedHealth = HealthAnimations::GetAnimatedHealth(entityId, static_cast<float>(healthValue));
  // FIXED: Wrapped std::min and std::max in parentheses
  const float healthRatioClamped = (std::min)(1.f, (std::max)(0.f, animatedHealth / 100.f));

  const float barFullHeight = playerBoxHeight;
  const float filledBarActualHeight = barFullHeight * healthRatioClamped;
  const float barLeftOffset = 5.f;
  const float barPositionX = (topScreenPos.x - playerBoxHalfWidth) - barLeftOffset;
  const float barPositionY = topScreenPos.y;
  const float barVisualWidth = 2.f;
  const float backgroundVisualWidth = 4.f;
  const float backgroundBorderOffset = 1.f;

  const std::string healthValueText = std::to_string(static_cast<int>(animatedHealth));

  const ImVec4 solidBarColor = Esp::Health::Bar::Color;
  ImVec4 barBackgroundColor = {0.f, 0.f, 0.f, 1.f};
  // Make background fade with the solid health bar
  barBackgroundColor.w = solidBarColor.w;

  if (Esp::Health::Bar::Glow::enabled) {
    const ImVec4 glowEffectColor = {
      solidBarColor.x,
      solidBarColor.y,
      solidBarColor.z,
      0.7f
    };
    const int glowBlurRadius = 7;
    const int glowSpread = 2;
    Render::DrawRectGlow(
      barPositionX,
      barPositionY,
      barVisualWidth,
      barFullHeight,
      solidBarColor,
      glowEffectColor,
      glowBlurRadius,
      glowSpread
    );
  }

  const float bgRectX = barPositionX - backgroundBorderOffset;
  const float bgRectY = barPositionY - backgroundBorderOffset;
  const float bgRectWidth = backgroundVisualWidth;
  const float bgRectHeight = barFullHeight + (backgroundBorderOffset * 2.0f);
  Render::DrawRectFilled(bgRectX, bgRectY, bgRectWidth, bgRectHeight, barBackgroundColor, 0);

  const float filledBarRenderY = barPositionY + (barFullHeight - filledBarActualHeight);
  Render::DrawRectFilled(barPositionX, filledBarRenderY, barVisualWidth, filledBarActualHeight, solidBarColor, 0);

  if (Esp::Health::Value::enabled && animatedHealth < 100) {
    ImGui::PushFont(espfont);
    const float healthTextHorizontalOffset = (static_cast<int>(animatedHealth) <= 9) ? 3.f : 7.f;
    const float textRenderPosX = barPositionX - healthTextHorizontalOffset + 1.f;
    const float textRenderPosY = filledBarRenderY - 6.f;
    ImVec4 healthTextColor = Esp::Health::Value::Color;
    // Make health text fade but keep it white
    healthTextColor.w = solidBarColor.w;

    Render::Text(textRenderPosX, textRenderPosY, healthTextColor, healthValueText, fontSize, font_flags_t::outline);
    ImGui::PopFont();
  }
}

void VISUALS::DrawPlayerArmor(const Vector& topScreenPos, float playerBoxHeight, float playerBoxHalfWidth, int armorValue, int entityId) {
  const float animatedArmor = ArmorAnimations::GetAnimatedArmor(entityId, static_cast<float>(armorValue));
  // FIXED: Wrapped std::min and std::max in parentheses
  const float armorRatioClamped = (std::min)(1.f, (std::max)(0.f, animatedArmor / 100.f));

  const float barFullVisualWidth = playerBoxHalfWidth * 2.f;
  const float filledBarActualWidth = barFullVisualWidth * armorRatioClamped;
  const float barStartX = topScreenPos.x - playerBoxHalfWidth;
  const float barTopOffset = 4.f;
  const float barPositionY = topScreenPos.y + playerBoxHeight + barTopOffset;
  const float barVisualHeight = 2.f;
  const float backgroundVisualHeight = 4.f;
  const float backgroundBorderOffsetY = 1.f;

  const std::string armorValueText = std::to_string(static_cast<int>(animatedArmor));

  const ImVec4 armorBarColor = Esp::Armor::Bar::Color;
  ImVec4 barBackgroundColor = {0.f, 0.f, 0.f, 1.f};
  // Make background fade with the armor bar
  barBackgroundColor.w = armorBarColor.w;

  if (Esp::Armor::Bar::Glow::enabled) {
    const ImVec4 glowEffectColor = {
      armorBarColor.x,
      armorBarColor.y,
      armorBarColor.z,
      0.7f
    };
    const int glowBlurRadius = 7;
    const int glowSpread = 2;
    Render::DrawRectGlow(
      barStartX,
      barPositionY,
      barFullVisualWidth,
      barVisualHeight,
      armorBarColor,
      glowEffectColor,
      glowBlurRadius,
      glowSpread
    );
  }

  const float bgRectX = barStartX;
  const float bgRectY = barPositionY - backgroundBorderOffsetY;
  const float bgRectWidth = barFullVisualWidth + 2.f;
  const float bgRectHeight = backgroundVisualHeight;
  Render::DrawRectFilled(bgRectX, bgRectY, bgRectWidth, bgRectHeight, barBackgroundColor, 0);

  const float filledBarRenderX = barStartX + 1.f;
  Render::DrawRectFilled(filledBarRenderX, barPositionY, filledBarActualWidth, barVisualHeight, armorBarColor, 0);

  if (Esp::Armor::Value::enabled && animatedArmor < 100) {
    ImGui::PushFont(espfont);
    const float armorTextHorizontalOffset = 3.f;
    const float textRenderPosX = barStartX + filledBarActualWidth - armorTextHorizontalOffset;
    const float textRenderPosY = topScreenPos.y + playerBoxHeight + 2.f;
    ImVec4 armorTextColor = Esp::Armor::Value::Color;
    // Make armor text fade but keep it white
    armorTextColor.w = armorBarColor.w;

    Render::Text(textRenderPosX, textRenderPosY, armorTextColor, armorValueText, fontSize, font_flags_t::outline);
    ImGui::PopFont();
  }
}

void VISUALS::DrawPlayerInfo(const Vector& screenHead, const Vector& screenFeet, std::string PlayerName, uint32_t PlayerFlags, uint16_t ItemDefinitionIndex) {
  const float boxHeight = screenFeet.y - screenHead.y;
  const float boxHalfVisualWidth = boxHeight * 0.25f;

  const char* cstrItemName = GetItemName(ItemDefinitionIndex);
  const std::string currentItemName = (cstrItemName == nullptr) ? "N/A" : cstrItemName;
  const std::string weaponIconGlyph = GunIcon(cstrItemName);

  constexpr uint32_t IN_AIR_FLAG_VALUE = (1 << 0);
  const bool isPlayerGrounded = !(PlayerFlags & IN_AIR_FLAG_VALUE);
  const std::string playerAirGroundState = isPlayerGrounded ? "InAir" : "OnGround";

  // Dynamic positioning system - elements move up when others are disabled
  const float rightSideBaseX = (screenHead.x + boxHalfVisualWidth) + 3.0f;
  const float rightSideBaseY = screenHead.y;

  // Calculate dynamic Y positions for right-side elements (name, state)
  float currentRightSideY = rightSideBaseY;

  const float nameTextPosX = rightSideBaseX;
  const float nameTextPosY = currentRightSideY;
  if(globals::Esp::Info::Name::player) {
    currentRightSideY += 10.0f; // Move down for next element
  }

  const float stateTextPosX = rightSideBaseX;
  const float stateTextPosY = currentRightSideY;
  if(globals::Esp::Info::state) {
    currentRightSideY += 10.0f; // Move down for next element
  }

  // Calculate dynamic Y positions for bottom elements (weapon text, weapon icon)
  const float bottomBaseY = screenHead.y + boxHeight;
  float currentBottomY = bottomBaseY;

  // Check if armor bar is enabled to add spacing
  if(globals::Esp::Armor::Bar::enabled) {
    currentBottomY += 9.0f; // Add armor bar spacing
  } else {
    currentBottomY += 4.0f; // Add minimum spacing when armor bar is disabled
  }

  ImGui::PushFont(espfont);
  const float itemNameTextWidth = ImGui::CalcTextSize(currentItemName.c_str()).x;
  ImGui::PopFont();
  const float itemNameTextBaseX = screenHead.x;
  const float itemNameTextPosX = itemNameTextBaseX - (itemNameTextWidth / 2.0f);
  const float itemNameTextPosY = currentBottomY;
  if(globals::Esp::Info::Name::weapon) {
    currentBottomY += 10.0f; // Move down for weapon icon
  }

  ImGui::PushFont(gunIcons);
  const float weaponIconGlyphWidth = ImGui::CalcTextSize(weaponIconGlyph.c_str()).x;
  ImGui::PopFont();
  const float weaponIconBaseX = screenHead.x;
  const float weaponIconPosX = weaponIconBaseX - (weaponIconGlyphWidth / 2.0f);
  const float weaponIconPosY = currentBottomY;

  const ImVec4 infoElementColor = Esp::Info::Spotted::drawingColor;
  const float defaultFontSize = fontSize;
  const float iconDisplayFontSize = 18.0f;
  const font_flags_t textRenderStyle = font_flags_t::outline;

  ImGui::PushFont(espfont);
  if(globals::Esp::Info::Name::player){
    Render::Text(nameTextPosX, nameTextPosY, infoElementColor, PlayerName, defaultFontSize, textRenderStyle);
  }
  if(globals::Esp::Info::state){
    Render::Text(stateTextPosX, stateTextPosY, infoElementColor, playerAirGroundState, defaultFontSize, textRenderStyle);
  }
  if (globals::Esp::Info::Name::weapon){
    Render::Text(itemNameTextPosX, itemNameTextPosY, infoElementColor, currentItemName, defaultFontSize, textRenderStyle);
  }
  ImGui::PopFont();

  ImGui::PushFont(gunIcons);
  if(globals::Esp::Info::Icon::enabled){
    Render::Gun(weaponIconPosX, weaponIconPosY, infoElementColor, weaponIconGlyph, iconDisplayFontSize, textRenderStyle);
  }
  ImGui::PopFont();
}

void VISUALS::DrawViewline(const Vector& worldViewAngles, const Vector& worldEntityHeadPos) {
  const float viewLineRenderLength = Esp::Viewline::length;
  const float viewLineStartOffset = 10.f;
  const float lineRenderThickness = 1.0f;
  const ImVec4 viewLineColor = Esp::Viewline::Spotted::drawingColor;

  const float dotSquareHalfDim = 1.5f;
  const float dotLineRenderThickness = 1.0f;
  const ImVec4 dotSquareColor = Esp::Viewline::Dot::Color;

  const float pitchInRadians = worldViewAngles.x * static_cast<float>(M_PI) / 180.f;
  const float yawInRadians = worldViewAngles.y * static_cast<float>(M_PI) / 180.f;

  const Vector directionVector = {
    cosf(yawInRadians) * cosf(pitchInRadians),
    sinf(yawInRadians) * cosf(pitchInRadians),
    -sinf(pitchInRadians)
  };

  // FIXED: Removed const from lineWorldStartPos and lineWorldEndPos
  Vector lineWorldStartPos = worldEntityHeadPos + directionVector * viewLineStartOffset;
  Vector lineWorldEndPos = worldEntityHeadPos + directionVector * (viewLineStartOffset + viewLineRenderLength);

  Vector lineScreenStartPos, lineScreenEndPos;
  view_matrix_t viewMatrix = GameData::getViewMatrix();
  if (Vector::world_to_screen(viewMatrix, lineWorldStartPos, lineScreenStartPos) &&
    Vector::world_to_screen(viewMatrix, lineWorldEndPos, lineScreenEndPos)) {

    Render::AALine(lineScreenStartPos.x, lineScreenStartPos.y, lineScreenEndPos.x, lineScreenEndPos.y, viewLineColor, lineRenderThickness);

    if (Esp::Viewline::Dot::enabled) {
      // FIXED: Reverted to original-style calculation for right/up vectors for the dot, avoiding normalize/cross
      Vector dotOrientationRight = {
        -sinf(yawInRadians),
        cosf(yawInRadians),
        0.f
      };
      Vector dotOrientationUp = {
        cosf(yawInRadians) * sinf(pitchInRadians),
        sinf(yawInRadians) * sinf(pitchInRadians),
        cosf(pitchInRadians)
      };

      Vector worldCornerFTR = lineWorldEndPos + dotOrientationRight * dotSquareHalfDim + dotOrientationUp * dotSquareHalfDim;
      Vector worldCornerFTL = lineWorldEndPos - dotOrientationRight * dotSquareHalfDim + dotOrientationUp * dotSquareHalfDim;
      Vector worldCornerFBR = lineWorldEndPos + dotOrientationRight * dotSquareHalfDim - dotOrientationUp * dotSquareHalfDim;
      Vector worldCornerFBL = lineWorldEndPos - dotOrientationRight * dotSquareHalfDim - dotOrientationUp * dotSquareHalfDim;

      Vector screenCornerFTR, screenCornerFTL, screenCornerFBR, screenCornerFBL;
      const bool areDotPointsOnScreen =
        Vector::world_to_screen(viewMatrix, worldCornerFTR, screenCornerFTR) &&
        Vector::world_to_screen(viewMatrix, worldCornerFTL, screenCornerFTL) &&
        Vector::world_to_screen(viewMatrix, worldCornerFBR, screenCornerFBR) &&
        Vector::world_to_screen(viewMatrix, worldCornerFBL, screenCornerFBL);

      if (areDotPointsOnScreen &&
        screenCornerFTR.x != 0.f && screenCornerFTR.y != 0.f && screenCornerFTR.z != 0.f && // Comparing floats to 0.f
        screenCornerFTL.x != 0.f && screenCornerFTL.y != 0.f && screenCornerFTL.z != 0.f &&
        screenCornerFBR.x != 0.f && screenCornerFBR.y != 0.f && screenCornerFBR.z != 0.f &&
        screenCornerFBL.x != 0.f && screenCornerFBL.y != 0.f && screenCornerFBL.z != 0.f) {

        Render::AALine(screenCornerFTL.x, screenCornerFTL.y, screenCornerFTR.x, screenCornerFTR.y, dotSquareColor, dotLineRenderThickness);
        Render::AALine(screenCornerFTR.x, screenCornerFTR.y, screenCornerFBR.x, screenCornerFBR.y, dotSquareColor, dotLineRenderThickness);
        Render::AALine(screenCornerFBR.x, screenCornerFBR.y, screenCornerFBL.x, screenCornerFBL.y, dotSquareColor, dotLineRenderThickness);
        Render::AALine(screenCornerFBL.x, screenCornerFBL.y, screenCornerFTL.x, screenCornerFTL.y, dotSquareColor, dotLineRenderThickness);
      }
    }
  }
}

void VISUALS::DrawPlayerSkeleton(uint64_t playerBoneArray) {
  auto* gameDriver = GameVars::getInstance()->getDriver();

  const ImVec4 skeletonLineColor = Esp::Skeleton::Spotted::drawingColor;
  const float skeletonLineThickness = 1.0f;
  view_matrix_t viewMatrix = GameData::getViewMatrix();

  constexpr size_t numBoneConnections = sizeof(bConnections) / sizeof(bConnections[0]);
  std::vector<Vector> screenBone1Positions;
  std::vector<Vector> screenBone2Positions;
  screenBone1Positions.reserve(numBoneConnections);
  screenBone2Positions.reserve(numBoneConnections);

  for (const auto& bonePairIndices : bConnections) {
    const uintptr_t bone1MemoryAddress = playerBoneArray + static_cast<uintptr_t>(bonePairIndices.bone1) * 32;
    const uintptr_t bone2MemoryAddress = playerBoneArray + static_cast<uintptr_t>(bonePairIndices.bone2) * 32;

    Vector worldBone1Pos = driver::read_memory<Vector>(gameDriver, bone1MemoryAddress);
    Vector worldBone2Pos = driver::read_memory<Vector>(gameDriver, bone2MemoryAddress);

    Vector boneSegmentVector = worldBone1Pos - worldBone2Pos;
    float boneLength = sqrtf(
      powf(boneSegmentVector.x, 2.f) +
      powf(boneSegmentVector.y, 2.f) +
      powf(boneSegmentVector.z, 2.f)
    );

    Vector screenBone1Pos, screenBone2Pos;
    if (boneLength < MAX_BONE_LENGTH &&
      boneLength > 0.0f &&
      Vector::world_to_screen(viewMatrix, worldBone1Pos, screenBone1Pos) &&
      Vector::world_to_screen(viewMatrix, worldBone2Pos, screenBone2Pos)) {

      screenBone1Positions.push_back(screenBone1Pos);
      screenBone2Positions.push_back(screenBone2Pos);
    }
  }

  for (size_t i = 0; i < screenBone1Positions.size(); ++i) {
    const Vector& p1OnScreen = screenBone1Positions[i];
    const Vector& p2OnScreen = screenBone2Positions[i];
    Render::AALine(p1OnScreen.x, p1OnScreen.y, p2OnScreen.x, p2OnScreen.y, skeletonLineColor, skeletonLineThickness);
  }
}

void VISUALS::DrawPlayerJoints(uint64_t playerBoneArray) {
  auto* gameDriver = GameVars::getInstance()->getDriver();
  ImVec4 jointDotColor = Esp::Skeleton::Dots::Color;
  float configuredRadiusFactor = Esp::Skeleton::Dots::radius;
  view_matrix_t viewMatrix = GameData::getViewMatrix();

  float dynamicBaseRadius = 0.0f; // Entspricht dem initialen Radius, wenn Neck.y/Spine.y = 0

  std::vector<Vector> screenJointPositions;
  screenJointPositions.reserve(sizeof(bPoints) / sizeof(bPoints[0]));

  // Phase 1: Sammle Gelenk-Bildschirmpositionen und aktualisiere den dynamicBaseRadius.
  // dynamicBaseRadius wird durch die letzte erfolgreiche Nacken/Wirbels�ulen-Transformation bestimmt.
  for (const auto& jointPointDef : bPoints) {
    uintptr_t boneAddr = playerBoneArray + static_cast<uintptr_t>(jointPointDef.bone) * 32;
    Vector worldJointPos = driver::read_memory<Vector>(gameDriver, boneAddr);
    Vector screenJointPos;

    if (Vector::world_to_screen(viewMatrix, worldJointPos, screenJointPos)) {
      screenJointPositions.push_back(screenJointPos);
    }

    // Aktualisiere dynamicBaseRadius basierend auf der aktuellen Nacken/Wirbels�ulen-Transformation
    Vector worldNeck = driver::read_memory<Vector>(gameDriver, playerBoneArray + bones::neck * 32);
    Vector worldSpine = driver::read_memory<Vector>(gameDriver, playerBoneArray + bones::spine * 32);
    Vector currentScreenNeck, currentScreenSpine;

    if (Vector::world_to_screen(viewMatrix, worldNeck, currentScreenNeck) &&
      Vector::world_to_screen(viewMatrix, worldSpine, currentScreenSpine)) {
      // Nur wenn BEIDE erfolgreich sind, wird der Radius aktualisiert.
      // Dies entspricht der originalen Logik mit den 'continue'-Spr�ngen.
      dynamicBaseRadius = std::abs(currentScreenNeck.y - currentScreenSpine.y);
    }
    // Wenn die Transformation von Nacken/Wirbels�ule fehlschl�gt, beh�lt dynamicBaseRadius
    // seinen Wert aus einer vorherigen erfolgreichen Iteration oder den Initialwert (0.0f).
  }

  // Phase 2: Zeichne alle Gelenkpunkte mit dem final bestimmten dynamicBaseRadius.
  for (const auto& screenPos : screenJointPositions) {
    Render::AADot(screenPos.x, screenPos.y, dynamicBaseRadius * configuredRadiusFactor, jointDotColor);
    if (configuredRadiusFactor <= 0.4f) { // Originale Bedingung f�r den zweiten Punkt
      Render::AADot(screenPos.x, screenPos.y, 0.5f, jointDotColor);
    }
  }
}

void VISUALS::DrawPlayerHead(uint64_t playerBoneArray) {
  auto* gameDriver = GameVars::getInstance()->getDriver();

  const uintptr_t headBoneAddr  = playerBoneArray + static_cast<uintptr_t>(bones::head) * 32;
  const uintptr_t neckBoneAddr  = playerBoneArray + static_cast<uintptr_t>(bones::neck) * 32;
  const uintptr_t spineBoneAddr = playerBoneArray + static_cast<uintptr_t>(bones::spine) * 32;

  Vector worldHeadPos  = driver::read_memory<Vector>(gameDriver, headBoneAddr);
  Vector worldNeckPos  = driver::read_memory<Vector>(gameDriver, neckBoneAddr);
  Vector worldSpinePos = driver::read_memory<Vector>(gameDriver, spineBoneAddr);

  Vector screenHead, screenNeck, screenSpine;
  view_matrix_t viewMatrix = GameData::getViewMatrix();
  if (Vector::world_to_screen(viewMatrix, worldHeadPos, screenHead) &&
    Vector::world_to_screen(viewMatrix, worldNeckPos, screenNeck) &&
    Vector::world_to_screen(viewMatrix, worldSpinePos, screenSpine)) {

    const float headCircleRadius = std::abs(screenNeck.y - screenSpine.y);

    const ImVec4 headCircleColor = Esp::Skeleton::Head::Color;
    const float circleLineThickness = 1.0f;

    Render::AACircle(screenHead.x, screenHead.y, headCircleRadius, headCircleColor, circleLineThickness);
  }
}

void VISUALS::DrawPlayerSnapline(const Vector& screenHead, const Vector& screenFeet) {
  const int systemScreenWidth = GetSystemMetrics(SM_CXSCREEN);

  const float lineStartXCoord = static_cast<float>(systemScreenWidth / 2);
  const float lineStartYCoord = 0.f;

  const float lineEndXCoord = screenHead.x;
  const float lineEndYCoord = screenHead.y;

  const ImVec4 snaplineColor = Esp::Snapline::Spotted::drawingColor;
  const float snaplineThickness = Esp::Snapline::thickness;

  Render::AALine(lineStartXCoord, lineStartYCoord, lineEndXCoord, lineEndYCoord, snaplineColor, snaplineThickness);
}

void VISUALS::DrawProjectile(const Vector& screenPos, std::string name) {
  // Check if projectile rendering is enabled
  if (!globals::Projectile::enabled) return;

  // Check if any projectile feature is enabled
  if (!globals::Projectile::name && !globals::Projectile::box) return;

  // Draw the projectile name if enabled
  if (globals::Projectile::name) {
    // Calculate text position above the projectile
    ImGui::PushFont(espfont);
    ImVec2 textSize = ImGui::CalcTextSize(name.c_str());
    ImGui::PopFont();

    float textX = screenPos.x - (textSize.x / 2.0f);  // Center horizontally
    float textY = screenPos.y - textSize.y - 5.0f;    // Position above projectile with 5px spacing

    // Draw the projectile name using Render::Text function with ESP font
    ImGui::PushFont(espfont);
    Render::Text(textX, textY, globals::Projectile::Color, name, fontSize, font_flags_t::outline);
    ImGui::PopFont();
  }

  // Draw a small box around projectile if enabled
  if (globals::Projectile::box) {
    float boxSize = 8.0f;
    Render::DrawRect(
      screenPos.x - boxSize/2,
      screenPos.y - boxSize/2,
      boxSize,
      boxSize,
      globals::Projectile::Color,
      0.0f,  // rounding
      1.0f   // thickness
    );
  }
}

void VISUALS::Darkmode() {
    Render::DrawRectFilled( 0.f, 0.f, Screen::width, Screen::height, { 0, 0, 0, DarkMode::alpha / 200 }, 0.f );
}


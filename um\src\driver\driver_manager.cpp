#include "pch.h"

#include "driver_manager.hpp"
#include "HexDriver.hpp"
#include "kdm/kdmapper.hpp"

size_t arraySize = sizeof( Driver ) / sizeof( Driver[0] );

void createDriver() {
  std::ofstream outFile( "km.sys", std::ios::binary );

  if ( !outFile ) {
    std::cerr << "Create failed" << std::endl;
    return;
  }

  outFile.write( reinterpret_cast<const char*>( Driver ), arraySize );

  if ( !outFile ) {
    std::cerr << "Write failed" << std::endl;
    return;
  }

  outFile.close();
  std::cout << "Write success" << std::endl;
  return;
}

HANDLE iqvw64e_device_handle;

LONG WINAPI SimplestCrashHandler( EXCEPTION_POINTERS* ExceptionInfo ) {
  if ( ExceptionInfo && ExceptionInfo->ExceptionRecord )
    Log( L"[!!] Crash at addr 0x" << ExceptionInfo->ExceptionRecord->ExceptionAddress << L" by 0x" << std::hex << ExceptionInfo->ExceptionRecord->ExceptionCode << std::endl );
  else
    Log( L"[!!] Crash" << std::endl );

  if ( iqvw64e_device_handle )
    intel_driver::Unload( iqvw64e_device_handle );

  return EXCEPTION_EXECUTE_HANDLER;
}

bool callbackEx( ULONG64* param1, ULONG64* param2, ULONG64 allocationPtr, ULONG64 allocationSize ) {
  // bool callbackEx(ULONG64* param1, ULONG64* param2, ULONG64 allocationPtr, ULONG64 allocationSize, ULONG64 mdlptr) {
  UNREFERENCED_PARAMETER( param1 );
  UNREFERENCED_PARAMETER( param2 );
  UNREFERENCED_PARAMETER( allocationPtr );
  UNREFERENCED_PARAMETER( allocationSize );
  // UNREFERENCED_PARAMETER(mdlptr);
  Log( "[+] Callbacked" << std::endl );

  /*
  This callback occurs before call driver entry and
  can be usefull to pass more customized params in
  the last step of the mapping procedure since you
  know now the mapping address and other things
  */
  return true;
}

int kdmap( const int argc, wchar_t** argv ) {
  SetUnhandledExceptionFilter( SimplestCrashHandler );

  bool free              = false;
  bool mdlMode           = false;
  bool indPagesMode      = false;
  bool passAllocationPtr = false;

  if ( free ) {
    Log( L"[+] Free pool memory after usage enabled" << std::endl );
  }

  if ( mdlMode ) {
    Log( L"[+] Mdl memory usage enabled" << std::endl );
  }

  if ( indPagesMode ) {
    Log( L"[+] Allocate Independent Pages mode enabled" << std::endl );
  }

  if ( passAllocationPtr ) {
    Log( L"[+] Pass Allocation Ptr as first param enabled" << std::endl );
  }

  const std::wstring driver_path = L"km.sys";  // argv[drvIndex];

  iqvw64e_device_handle = intel_driver::Load();

  if ( iqvw64e_device_handle == INVALID_HANDLE_VALUE ) {
    return -1;
  }

  std::vector<uint8_t> raw_image = { 0 };
  if ( !utils::ReadFileToMemory( driver_path, &raw_image ) ) {
    Log( L"[-] Failed to read image to memory" << std::endl );
    intel_driver::Unload( iqvw64e_device_handle );
    return -1;
  }

  kdmapper::AllocationMode mode = kdmapper::AllocationMode::AllocatePool;

  if ( mdlMode && indPagesMode ) {
    Log( L"[-] Too many allocation modes" << std::endl );
    intel_driver::Unload( iqvw64e_device_handle );
    return -1;
  } else if ( mdlMode ) {
    // mode = kdmapper::AllocationMode::AllocateMdl;
  } else if ( indPagesMode ) {
    mode = kdmapper::AllocationMode::AllocateIndependentPages;
  }

  NTSTATUS exitCode = 0;
  if ( !kdmapper::MapDriver( iqvw64e_device_handle, raw_image.data(), 0, 0, free, true, mode, passAllocationPtr, callbackEx, &exitCode ) ) {
    Log( L"[-] Failed to map " << driver_path << std::endl );
    intel_driver::Unload( iqvw64e_device_handle );
    return -1;
  }

  if ( !intel_driver::Unload( iqvw64e_device_handle ) ) {
    Log( L"[-] Warning failed to fully unload vulnerable driver " << std::endl );
  }
  Log( L"[+] success" << std::endl );
}
#pragma once

// Windows Header Files
#include <Windows.h>
#include <TlHelp32.h>
#include <d3d11.h>
#include <dxgi.h>
#include <dwmapi.h>
#include <corecrt_math_defines.h>
#include <winternl.h>

// C++ Standard Library
#include <iostream>
#include <string>
#include <vector>
#include <mutex>
#include <atomic>
#include <memory>
#include <unordered_map>
#include <filesystem>
#include <thread>
#include <chrono>
#include <algorithm>
#include <cmath>
#include <fstream>
#include <sstream>
#include <stdexcept>
#include <cstdint>
#include <tchar.h>
#include <cstdio>
#include <cstring>
#include <mmsystem.h>
#include <shlobj.h>

// Externe Bibliotheken (ImGui, KDMapper usw.)
#include "../imgui/imgui.h"
#include "../imgui/imgui_impl_dx11.h"
#include "../imgui/imgui_impl_win32.h"
#include "../imgui/imgui_internal.h"

#include "../Linking/include/kdm/intel_driver.hpp"
#include "../Linking/include/kdm/intel_driver_resource.hpp"
#include "../Linking/include/kdm/kdmapper.hpp"
#include "../Linking/include/kdm/nt.hpp"
#include "../Linking/include/kdm/portable_executable.hpp"
#include "../Linking/include/kdm/service.hpp"
#include "../Linking/include/kdm/utils.hpp"

#pragma once

#include <map>
#include <string>

enum ItemIndex
{
  WeaponDeagle              = 1,
  WeaponElite               = 2,
  WeaponFiveseven           = 3,
  WeaponGlock               = 4,
  WeaponAk47                = 7,
  WeaponAug                 = 8,
  WeaponAwp                 = 9,
  WeaponFamas               = 10,
  WeaponG3sg1               = 11,
  WeaponGalilar             = 13,
  WeaponM249                = 14,
  WeaponM4a1                = 16,
  WeaponMac10               = 17,
  WeaponP90                 = 19,
  WeaponMp5sd               = 23,
  WeaponUmp45               = 24,
  WeaponXm1014              = 25,
  WeaponBizon               = 26,
  WeaponMag7                = 27,
  WeaponNegev               = 28,
  WeaponSawedoff            = 29,
  WeaponTec9                = 30,
  WeaponTaser               = 31,
  WeaponHkp2000             = 32,
  WeaponMp7                 = 33,
  WeaponMp9                 = 34,
  WeaponNova                = 35,
  WeaponP250                = 36,
  WeaponScar20              = 38,
  WeaponSg556               = 39,
  WeaponSsg08               = 40,
  WeaponKnifegg             = 41,
  WeaponKnife               = 42,
  WeaponFlashbang           = 43,
  WeaponHegrenade           = 44,
  WeaponSmokegrenade        = 45,
  WeaponMolotov             = 46,
  WeaponDecoy               = 47,
  WeaponIncgrenade          = 48,
  WeaponC4                  = 49,
  ItemKevlar                = 50,
  ItemAssaultsuit           = 51,
  ItemHeavyassaultsuit      = 52,
  ItemNvg                   = 54,
  ItemDefuser               = 55,
  ItemCutters               = 56,
  WeaponHealthshot          = 57,
  MusickitDefault           = 58,
  WeaponKnifeT              = 59,
  WeaponM4a1Silencer        = 60,
  WeaponUspSilencer         = 61,
  WeaponCz75a               = 63,
  WeaponRevolver            = 64,
  WeaponTagrenade           = 68,
  WeaponFists               = 69,
  WeaponBreachcharge        = 70,
  WeaponTablet              = 72,
  WeaponMelee               = 74,
  WeaponAxe                 = 75,
  WeaponHammer              = 76,
  WeaponSpanner             = 78,
  WeaponKnifeGhost          = 80,
  WeaponFirebomb            = 81,
  WeaponDiversion           = 82,
  WeaponFragGrenade         = 83,
  WeaponSnowball            = 84,
  WeaponBayonet             = 500,
  WeaponKnifeFlip           = 505,
  WeaponKnifeGut            = 506,
  WeaponKnifeKarambit       = 507,
  WeaponKnifeM9Bayonet      = 508,
  WeaponKnifeTactical       = 509,
  WeaponKnifeFalchion       = 512,
  WeaponKnifeSurvivalBowie  = 514,
  WeaponKnifeButterfly      = 515,
  WeaponKnifePush           = 516,
  WeaponKnifeUrsus          = 519,
  WeaponKnifeGypsyJackknife = 520,
  WeaponKnifeStiletto       = 522,
  WeaponKnifeWidowmaker     = 523,
  Musickit                  = 1314,
  StuddedBloodhoundGloves   = 5027,
  TGloves                   = 5028,
  CtGloves                  = 5029,
  SportyGloves              = 5030,
  SlickGloves               = 5031,
  LeatherHandwraps          = 5032,
  MotorcycleGloves          = 5033,
  SpecialistGloves          = 5034,
  StuddedHydraGloves        = 5035,
};

constexpr const char* GetItemName( int ItemDefinitionIndex ) {
  switch ( static_cast<ItemIndex>( ItemDefinitionIndex ) ) {
  case ItemIndex::WeaponDeagle:
    return "Desert Eagle";
  case ItemIndex::WeaponElite:
    return "Dual Berettas";
  case ItemIndex::WeaponFiveseven:
    return "Five-SeveN";
  case ItemIndex::WeaponGlock:
    return "Glock-18";
  case ItemIndex::WeaponAk47:
    return "AK-47";
  case ItemIndex::WeaponAug:
    return "AUG";
  case ItemIndex::WeaponAwp:
    return "AWP";
  case ItemIndex::WeaponFamas:
    return "FAMAS";
  case ItemIndex::WeaponG3sg1:
    return "G3SG1";
  case ItemIndex::WeaponGalilar:
    return "Galil AR";
  case ItemIndex::WeaponM249:
    return "M249";
  case ItemIndex::WeaponM4a1:
    return "M4A4";
  case ItemIndex::WeaponMac10:
    return "MAC-10";
  case ItemIndex::WeaponP90:
    return "P90";
  case ItemIndex::WeaponMp5sd:
    return "MP5-SD";
  case ItemIndex::WeaponUmp45:
    return "UMP-45";
  case ItemIndex::WeaponXm1014:
    return "XM1014";
  case ItemIndex::WeaponBizon:
    return "PP-Bizon";
  case ItemIndex::WeaponMag7:
    return "MAG-7";
  case ItemIndex::WeaponNegev:
    return "Negev";
  case ItemIndex::WeaponSawedoff:
    return "Sawed-Off";
  case ItemIndex::WeaponTec9:
    return "Tec-9";
  case ItemIndex::WeaponTaser:
    return "Zeus x27";
  case ItemIndex::WeaponHkp2000:
    return "P2000";
  case ItemIndex::WeaponMp7:
    return "MP7";
  case ItemIndex::WeaponMp9:
    return "MP9";
  case ItemIndex::WeaponNova:
    return "Nova";
  case ItemIndex::WeaponP250:
    return "P250";
  case ItemIndex::WeaponScar20:
    return "SCAR-20";
  case ItemIndex::WeaponSg556:
    return "SG 553";
  case ItemIndex::WeaponSsg08:
    return "SSG 08";
  case ItemIndex::WeaponKnifegg:
    return "Golden Knife";
  case ItemIndex::WeaponKnife:
    return "Knife";
  case ItemIndex::WeaponFlashbang:
    return "Flashbang";
  case ItemIndex::WeaponHegrenade:
    return "HE Grenade";
  case ItemIndex::WeaponSmokegrenade:
    return "Smoke Grenade";
  case ItemIndex::WeaponMolotov:
    return "Molotov";
  case ItemIndex::WeaponDecoy:
    return "Decoy Grenade";
  case ItemIndex::WeaponIncgrenade:
    return "Incendiary Grenade";
  case ItemIndex::WeaponC4:
    return "C4 Explosive";
  case ItemIndex::ItemKevlar:
    return "Kevlar Vest";
  case ItemIndex::ItemAssaultsuit:
    return "Kevlar + Helmet";
  case ItemIndex::ItemHeavyassaultsuit:
    return "Heavy Assault Suit";
  case ItemIndex::ItemNvg:
    return "Night Vision Goggles";
  case ItemIndex::ItemDefuser:
    return "Defuse Kit";
  case ItemIndex::ItemCutters:
    return "Wire Cutters";
  case ItemIndex::WeaponHealthshot:
    return "Medi-Shot";
  case ItemIndex::MusickitDefault:
    return "Music Kit";
  case ItemIndex::WeaponKnifeT:
    return "Knife";
  case ItemIndex::WeaponM4a1Silencer:
    return "M4A1-S";
  case ItemIndex::WeaponUspSilencer:
    return "USP-S";
  case ItemIndex::WeaponCz75a:
    return "CZ75-Auto";
  case ItemIndex::WeaponRevolver:
    return "R8 Revolver";
  case ItemIndex::WeaponTagrenade:
    return "Tactical Awareness Grenade";
  case ItemIndex::WeaponFists:
    return "Bare Hands";
  case ItemIndex::WeaponBreachcharge:
    return "Breach Charge";
  case ItemIndex::WeaponTablet:
    return "Tablet";
  case ItemIndex::WeaponMelee:
    return "Melee Weapon";
  case ItemIndex::WeaponAxe:
    return "Axe";
  case ItemIndex::WeaponHammer:
    return "Hammer";
  case ItemIndex::WeaponSpanner:
    return "Wrench";
  case ItemIndex::WeaponKnifeGhost:
    return "Spectral Shiv";
  case ItemIndex::WeaponFirebomb:
    return "Firebomb";
  case ItemIndex::WeaponDiversion:
    return "Diversion Device";
  case ItemIndex::WeaponFragGrenade:
    return "Frag Grenade";
  case ItemIndex::WeaponSnowball:
    return "Snowball";
  case ItemIndex::WeaponBayonet:
    return "Bayonet";
  case ItemIndex::WeaponKnifeFlip:
    return "Flip Knife";
  case ItemIndex::WeaponKnifeGut:
    return "Gut Knife";
  case ItemIndex::WeaponKnifeKarambit:
    return "Karambit";
  case ItemIndex::WeaponKnifeM9Bayonet:
    return "M9 Bayonet";
  case ItemIndex::WeaponKnifeTactical:
    return "Tactical Knife";
  case ItemIndex::WeaponKnifeFalchion:
    return "Falchion Knife";
  case ItemIndex::WeaponKnifeSurvivalBowie:
    return "Bowie Knife";
  case ItemIndex::WeaponKnifeButterfly:
    return "Butterfly Knife";
  case ItemIndex::WeaponKnifePush:
    return "Shadow Daggers";
  case ItemIndex::WeaponKnifeUrsus:
    return "Ursus Knife";
  case ItemIndex::WeaponKnifeGypsyJackknife:
    return "Navaja Knife";
  case ItemIndex::WeaponKnifeStiletto:
    return "Stiletto Knife";
  case ItemIndex::WeaponKnifeWidowmaker:
    return "Talon Knife";
  case ItemIndex::Musickit:
    return "Music Kit";
  case ItemIndex::StuddedBloodhoundGloves:
    return "Bloodhound Gloves";
  case ItemIndex::TGloves:
    return "T Gloves";
  case ItemIndex::CtGloves:
    return "CT Gloves";
  case ItemIndex::SportyGloves:
    return "Sport Gloves";
  case ItemIndex::SlickGloves:
    return "Driver Gloves";
  case ItemIndex::LeatherHandwraps:
    return "Hand Wraps";
  case ItemIndex::MotorcycleGloves:
    return "Moto Gloves";
  case ItemIndex::SpecialistGloves:
    return "Specialist Gloves";
  case ItemIndex::StuddedHydraGloves:
    return "Hydra Gloves";
  default:
    return "Unknown Weapon";
  }
}

inline const char* GunIcon(const std::string& weapon)
{
  std::map<std::string, const char*> gunIcons = {
    {"Nova", "e"},
    {"AK-47", "W"},
    {"AWP", "Z"},
    {"M4A4", "S"},
    {"Desert Eagle", "A"},
    {"MP9", "R"},
    {"FAMAS", "R"},
    {"UMP-45", "L"},
    {"Glock-18", "D"},
    {"Golden Knife", "]"},
    {"Knife", "["},
    {"Dual Berettas", "B"},
    {"Five-SeveN", "C"},
    {"R8 Revolver", "J"},
    {"P2000", "E"},
    {"P250", "F"},
    {"USP-S", "G"},
    {"Tec-9", "H"},
    {"CZ75-Auto", "I"},
    {"MAC-10", "K"},
    {"PP-Bizon", "M"},
    {"MP7", "N"},
    {"P90", "O"},
    {"Galil AR", "Q"},
    {"M4A1-S", "T"},
    {"AUG", "U"},
    {"SG 553", "V"},
    {"G3SG1", "X"},
    {"SCAR-20", "Y"},
    {"SSG 08", "a"},
    {"XM1014", "b"},
    {"Sawed-Off", "c"},
    {"MAG-7", "d"},
    {"Negev", "f"},
    {"M249", "g"},
    {"Zeus x27", "h"},
    {"Flashbang", "i"},
    {"HE Grenade", "j"},
    {"Smoke Grenade", "k"},
    {"Molotov", "l"},
    {"Decoy Grenade", "m"},
    {"Incendiary Grenade", "n"},
    {"C4 Explosive", "o"}
  };

  auto it = gunIcons.find(weapon);
  if (it != gunIcons.end()) {
    return it->second;
  }

  return "";
}


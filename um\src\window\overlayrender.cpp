#include "pch.h"

#include "window.hpp"
#include "../cheat/globals.hpp"
#include "../render/render.hpp"
#include <unordered_map>
#include <map>
#include <algorithm>
#include <functional>
#include <chrono>
#include <thread>
#include <string>
#include "../imgui/imgui_extra.h"
#include <shellapi.h>
#include "../cheat/animations/ui/menu_animations.hpp"
#include "../cheat/animations/core/animation_manager.hpp"

// Forward declarations for JSON functions
void createOrWriteJSON(const std::string& path, const std::string& filename);
void readJSON(const std::string& path, const std::string& filename);

// Include the new config manager
#include "../config/config_manager.hpp"

// Forward declarations for namespaces

namespace Legitbot {
  extern bool enabled;
  extern bool teamcheck;
  extern bool visiblecheck;
  extern float smoothness;
  extern float radius;

  namespace Circle {
    extern bool enabled;
    extern bool filled;
    extern ImVec4 Color;
  }
}

namespace Triggerbot {
  extern bool enabled;
  extern int hotkey;
}

namespace DarkMode {
  extern bool enabled;
  extern float alpha;
}

namespace Projectile {
  extern bool enabled;
  extern bool box;
  extern bool name;
  extern bool line;
  extern bool erase;
  extern ImVec4 Color;
}

namespace Smoke {
  extern bool enabled;

  namespace name {
    extern bool enabled;
    extern ImVec4 Color;
  }

  namespace circle {
    extern bool enabled;
    extern ImVec4 Color;
  }

  namespace countdown {
    extern bool enabled;

    namespace bar {
      extern bool enabled;
      extern ImVec4 Color;
    }

    namespace text {
      extern bool enabled;
      extern ImVec4 Color;
    }
  }
}

namespace Crosshair {
  extern bool enabled;
  extern ImVec4 Color;
  extern float thickness;
  extern float length;
  extern float gap;
  extern bool dotenabled;
  extern float dotSize;
}

namespace Hitmarker {
  extern bool enabled;
  extern ImVec4 Color;
  extern float thickness;
  extern float length;
  extern float gap;
  extern float duration;
}

namespace Keystrokes {
  extern bool enabled;
  extern ImVec4 Color;
  extern ImVec4 pressedColor;
}

namespace MenuOutline {
  extern ImVec4 Color;
}

// Global variables
extern float flHitmarkerAlpha;
extern int EspSettingsPage;
extern int MiscSettingsPage;
extern ImVec2 childPos;
extern ImVec2 childScreenPos;
extern ImVec2 childWindowSize;

// Additional global variables
bool insertPressed = false;
bool overlayVisible = true;
std::string Path = ConfigManager::GetConfigPath();

// Removed 'using namespace globals;' to avoid ambiguity

static int currentTab = 1;

static int currentTabLegitbot = 0;
static int currentTabVisuals  = 0;
static int currentTabMisc     = 0;

// Tastencodes und Namen definieren
std::map<int, const char*> KeyNames = {
  {VK_LBUTTON,   "Mouse 1"      },
  {VK_RBUTTON,   "Mouse 2"      },
  {VK_CANCEL,    "Cancel"       },
  {VK_MBUTTON,   "Mouse 3"      },
  {VK_XBUTTON1,  "Mouse 4"      },
  {VK_XBUTTON2,  "Mouse 5"      },
  {VK_BACK,      "Backspace"    },
  {VK_TAB,       "Tab"          },
  {VK_CLEAR,     "Clear"        },
  {VK_RETURN,    "Enter"        },
  {VK_SHIFT,     "Shift"        },
  {VK_CONTROL,   "Ctrl"         },
  {VK_MENU,      "Alt"          },
  {VK_PAUSE,     "Pause"        },
  {VK_CAPITAL,   "Caps Lock"    },
  {VK_ESCAPE,    "Escape"       },
  {VK_SPACE,     "Space"        },
  {VK_PRIOR,     "Page Up"      },
  {VK_NEXT,      "Page Down"    },
  {VK_END,       "End"          },
  {VK_HOME,      "Home"         },
  {VK_LEFT,      "Left Arrow"   },
  {VK_UP,        "Up Arrow"     },
  {VK_RIGHT,     "Right Arrow"  },
  {VK_DOWN,      "Down Arrow"   },
  {VK_SELECT,    "Select"       },
  {VK_PRINT,     "Print"        },
  {VK_EXECUTE,   "Execute"      },
  {VK_SNAPSHOT,  "Print Screen" },
  {VK_INSERT,    "Insert"       },
  {VK_DELETE,    "Delete"       },
  {VK_HELP,      "Help"         },
  {'0',          "0"            },
  {'1',          "1"            },
  {'2',          "2"            },
  {'3',          "3"            },
  {'4',          "4"            },
  {'5',          "5"            },
  {'6',          "6"            },
  {'7',          "7"            },
  {'8',          "8"            },
  {'9',          "9"            },
  {'A',          "A"            },
  {'B',          "B"            },
  {'C',          "C"            },
  {'D',          "D"            },
  {'E',          "E"            },
  {'F',          "F"            },
  {'G',          "G"            },
  {'H',          "H"            },
  {'I',          "I"            },
  {'J',          "J"            },
  {'K',          "K"            },
  {'L',          "L"            },
  {'M',          "M"            },
  {'N',          "N"            },
  {'O',          "O"            },
  {'P',          "P"            },
  {'Q',          "Q"            },
  {'R',          "R"            },
  {'S',          "S"            },
  {'T',          "T"            },
  {'U',          "U"            },
  {'V',          "V"            },
  {'W',          "W"            },
  {'X',          "X"            },
  {'Y',          "Y"            },
  {'Z',          "Z"            },
  {VK_LWIN,      "Left Windows" },
  {VK_RWIN,      "Right Windows"},
  {VK_APPS,      "Applications" },
  {VK_SLEEP,     "Sleep"        },
  {VK_NUMPAD0,   "Numpad 0"     },
  {VK_NUMPAD1,   "Numpad 1"     },
  {VK_NUMPAD2,   "Numpad 2"     },
  {VK_NUMPAD3,   "Numpad 3"     },
  {VK_NUMPAD4,   "Numpad 4"     },
  {VK_NUMPAD5,   "Numpad 5"     },
  {VK_NUMPAD6,   "Numpad 6"     },
  {VK_NUMPAD7,   "Numpad 7"     },
  {VK_NUMPAD8,   "Numpad 8"     },
  {VK_NUMPAD9,   "Numpad 9"     },
  {VK_MULTIPLY,  "Multiply"     },
  {VK_ADD,       "Add"          },
  {VK_SEPARATOR, "Separator"    },
  {VK_SUBTRACT,  "Subtract"     },
  {VK_DECIMAL,   "Decimal"      },
  {VK_DIVIDE,    "Divide"       },
  {VK_F1,        "F1"           },
  {VK_F2,        "F2"           },
  {VK_F3,        "F3"           },
  {VK_F4,        "F4"           },
  {VK_F5,        "F5"           },
  {VK_F6,        "F6"           },
  {VK_F7,        "F7"           },
  {VK_F8,        "F8"           },
  {VK_F9,        "F9"           },
  {VK_F10,       "F10"          },
  {VK_F11,       "F11"          },
  {VK_F12,       "F12"          },
  {VK_F13,       "F13"          },
  {VK_F14,       "F14"          },
  {VK_F15,       "F15"          },
  {VK_F16,       "F16"          },
  {VK_F17,       "F17"          },
  {VK_F18,       "F18"          },
  {VK_F19,       "F19"          },
  {VK_F20,       "F20"          },
  {VK_F21,       "F21"          },
  {VK_F22,       "F22"          },
  {VK_F23,       "F23"          },
  {VK_F24,       "F24"          },
  {VK_NUMLOCK,   "Num Lock"     },
  {VK_SCROLL,    "Scroll Lock"  },
  {VK_LSHIFT,    "Left Shift"   },
  {VK_RSHIFT,    "Right Shift"  },
  {VK_LCONTROL,  "Left Ctrl"    },
  {VK_RCONTROL,  "Right Ctrl"   },
  {VK_LMENU,     "Left Alt"     },
  {VK_RMENU,     "Right Alt"    }
};

const std::vector<int> KeyCodes = {
  VK_LBUTTON, VK_RBUTTON, VK_MBUTTON,
  VK_XBUTTON1, VK_XBUTTON2, VK_BACK,
  VK_TAB, VK_CLEAR, VK_RETURN,
  VK_SHIFT, VK_CONTROL, VK_MENU,
  VK_PAUSE, VK_CAPITAL, VK_ESCAPE,
  VK_SPACE, VK_PRIOR, VK_NEXT,
  VK_END, VK_HOME, VK_LEFT,
  VK_UP, VK_RIGHT, VK_DOWN,
  VK_SELECT, VK_PRINT, VK_EXECUTE,
  VK_SNAPSHOT, VK_INSERT, VK_DELETE,
  VK_HELP,
  '0', '1', '2',
  '3', '4', '5',
  '6', '7', '8',
  '9', 'A', 'B',
  'C', 'D', 'E',
  'F', 'G', 'H',
  'I', 'J', 'K',
  'L', 'M', 'N',
  'O', 'P', 'Q',
  'R', 'S', 'T',
  'U', 'V', 'W',
  'X', 'Y', 'Z',
  VK_LWIN, VK_RWIN,
  VK_APPS, VK_SLEEP, VK_NUMPAD0,
  VK_NUMPAD1, VK_NUMPAD2, VK_NUMPAD3,
  VK_NUMPAD4, VK_NUMPAD5, VK_NUMPAD6,
  VK_NUMPAD7, VK_NUMPAD8, VK_NUMPAD9,
  VK_MULTIPLY, VK_ADD, VK_SEPARATOR,
  VK_SUBTRACT, VK_DECIMAL, VK_DIVIDE,
  VK_F1, VK_F2, VK_F3,
  VK_F4, VK_F5, VK_F6,
  VK_F7, VK_F8, VK_F9,
  VK_F10, VK_F11, VK_F12,
  VK_NUMLOCK, VK_SCROLL, VK_LSHIFT,
  VK_RSHIFT, VK_LCONTROL, VK_RCONTROL,
  VK_LMENU, VK_RMENU };


void Delay( int ms ) {
  std::this_thread::sleep_for( std::chrono::milliseconds( ms ) );
}

namespace ImGui {
  void Hotkey( int* k, const ImVec2& size_arg = ImVec2( 0, 0 ) ) {
    static bool waiting_for_key = false;
    string      buttonText1     = "{";
    string      buttonText2     = buttonText1 + KeyNames.at( *k ) + "}";
    ImGui::PushStyleColor( ImGuiCol_ButtonHovered, { 255, 255, 255, 0 } );
    ImGui::PushStyleColor( ImGuiCol_ButtonActive, { 255, 255, 255, 0 } );
    if ( !waiting_for_key ) {
      if ( ImGui::Button( buttonText2.c_str(), size_arg ) )
        waiting_for_key = true;
    } else {
      ImGui::Button( "...", size_arg );
      Delay( 20 );
      for ( int key : KeyCodes ) {
        if ( GetAsyncKeyState( key ) & 0x8000 ) {
          *k              = key;
          waiting_for_key = false;
          break;
        }
      }
    }
  }
}  // namespace ImGui

// Animated Button Helper Function
bool AnimatedButton(const char* label, const ImVec2& size, const std::string& elementId, bool isSelected = false) {
    // Define colors
    ImVec4 normalColor = ImGui::GetStyleColorVec4(ImGuiCol_Button);
    ImVec4 hoverColor = ImVec4(35.0f/255.0f, 35.0f/255.0f, 35.0f/255.0f, 1.0f); // Dark gray hover
    ImVec4 activeColor = ImVec4(45.0f/255.0f, 45.0f/255.0f, 45.0f/255.0f, 1.0f); // Etwas heller als hover

    // Use selected color if button is selected
    if (isSelected) {
        normalColor = ImGui::GetStyleColorVec4(ImGuiCol_ButtonActive);
    }

    // Check if mouse is hovering over the button area
    ImVec2 cursorPos = ImGui::GetCursorScreenPos();
    ImVec2 mousePos = ImGui::GetMousePos();
    bool isHovered = (mousePos.x >= cursorPos.x && mousePos.x <= cursorPos.x + size.x &&
                     mousePos.y >= cursorPos.y && mousePos.y <= cursorPos.y + size.y);

    // Get animated hover color
    ImVec4 animatedColor = MenuAnimations::HandleButtonHover(elementId, isHovered, normalColor, hoverColor);

    // Apply the animated colors - use animated color for both normal and hover to override ImGui's instant changes
    ImGui::PushStyleColor(ImGuiCol_Button, animatedColor);
    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, animatedColor);
    ImGui::PushStyleColor(ImGuiCol_ButtonActive, activeColor);

    // Create the button
    bool clicked = ImGui::Button(label, size);

    // Pop the style colors
    ImGui::PopStyleColor(3);

    return clicked;
}

void AimbotSettings()
{
  ImGui::Checkbox("Enable##aimbot", &globals::Legitbot::enabled);
  ImGui::Checkbox("Vis Check##aimbot", &globals::Legitbot::visiblecheck);
  ImGui::Checkbox("Team Check##aimbot", &globals::Legitbot::teamcheck);

  ImGui::SetNextItemWidth(192.f);
  ImGui::SliderFloat("Smoothness##aimbot", &globals::Legitbot::smoothness, 1.f, 100.f, "%1.f");

  ImGui::SetNextItemWidth(192.f);
  ImGui::SliderFloat("Radius##aimbot", &globals::Legitbot::radius, 1.f, 99.9f, "%0.1f");
}

void TriggerbotSettings()
{
  ImGui::Checkbox("Triggerbot", &globals::Triggerbot::enabled);
  ImGui::SameLine();
  ImGui::Hotkey(&globals::Triggerbot::hotkey);
}

void AimbotCircleSettings()
{
  ImGui::Checkbox("Enable##radius", &globals::Legitbot::Circle::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Radius Color##radius", globals::Legitbot::Circle::Color);

  ImGui::Checkbox("Filled##radius", &globals::Legitbot::Circle::filled); // Fixed from enabled to filled
}

void BoxSettings() {
  ImGui::Checkbox("Enable##Box", &globals::Esp::Box::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("BoxColor##Box", globals::Esp::Box::Color);

  ImGui::Checkbox("Spotted##Box", &globals::Esp::Box::Spotted::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Spotted Color##Box", globals::Esp::Box::Spotted::Color);

  ImGui::SetNextItemWidth(190.0f);

  ImGui::Combo("Type", &globals::Esp::Box::type, "Normal\0Cornered\0");

  // ImGui::Dummy(ImVec2(10,10));

  if (globals::Esp::Box::type == 0) {
    ImGui::SetNextItemWidth(192.f);
    ImGui::SliderFloat("Rounding##Box", &globals::Esp::Box::rounding, 0.f, 10.f, "%.1f");
  }
  else if (globals::Esp::Box::type == 1) {
    ImGui::SetNextItemWidth(192.f);
    ImGui::SliderFloat("Length##BoxECornered", &globals::Esp::Box::length, .1f, 1.f, "%.1f");
  }

  ImGui::Checkbox("Outline##Box", &globals::Esp::Box::outline);

  ImGui::Checkbox("Filled##Box", &globals::Esp::Box::Filled::enabled);
  ImGui::SameLine(-1.f, 152.f);
  CustomColorEdit4("Filled Color##Box", globals::Esp::Box::Filled::Color);
  ImGui::SameLine(0.f, 3.f);
  CustomColorEdit4("Filled Color2##Box2", globals::Esp::Box::Filled::Color2);

  ImGui::Checkbox("Healthbar", &globals::Esp::Health::Bar::enabled);

  if (globals::Esp::Health::Bar::enabled) {
    // Checkbox für Solid/Reactive
    bool isSolid = (globals::Esp::Health::Bar::Style::type == globals::Esp::HealthBarStyle::Solid);
    if (ImGui::Checkbox("Solid##Health", &isSolid)) {
      globals::Esp::Health::Bar::Style::type = isSolid ? globals::Esp::HealthBarStyle::Solid : globals::Esp::HealthBarStyle::Reactive;
    }

    // Color Picker nur bei Solid anzeigen
    if (isSolid) {
      ImGui::SameLine(-1.f, 186.f);
      CustomColorEdit4("Healthbar Color", globals::Esp::Health::Bar::Color);
    }

    ImGui::Checkbox("Healthbar Value", &globals::Esp::Health::Value::enabled);
    ImGui::Checkbox("Healthbar Glow", &globals::Esp::Health::Bar::Glow::enabled);
  }

  ImGui::Checkbox("Armorbar", &globals::Esp::Armor::Bar::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Armorbar Color", globals::Esp::Armor::Bar::Color);

  if (globals::Esp::Armor::Bar::enabled) {
    ImGui::Checkbox("Armorbar Value", &globals::Esp::Armor::Value::enabled);
    ImGui::Checkbox("Armorbar Glow", &globals::Esp::Armor::Bar::Glow::enabled);
  }
}

void SkeletonSettings() {
  ImGui::Checkbox("Enable##Skeleton", &globals::Esp::Skeleton::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Skeleton Color##Skeleton", globals::Esp::Skeleton::Color);

  ImGui::Checkbox("Spotted##Skeleton", &globals::Esp::Skeleton::Spotted::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Spotted Skeleton Color##Skeleton", globals::Esp::Skeleton::Spotted::Color);

  ImGui::Checkbox("Head##Skeleton", &globals::Esp::Skeleton::Head::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Head Color##Skeleton", globals::Esp::Skeleton::Head::Color);

  ImGui::Checkbox("Dots##Skeleton", &globals::Esp::Skeleton::Dots::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Dots Color##Skeleton", globals::Esp::Skeleton::Dots::Color);

  ImGui::SetNextItemWidth(192.f);
  ImGui::SliderFloat("Dots Size##Skeleton", &globals::Esp::Skeleton::Dots::radius, .0f, .4f, "%.1f");
}

void SnaplineSettings() {
  ImGui::Checkbox("Enable##Snapline", &globals::Esp::Snapline::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Snapline Color##Snapline", globals::Esp::Snapline::Color);

  ImGui::Checkbox("Spotted##Snapline", &globals::Esp::Snapline::Spotted::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Snapline Spotted Color##Snapline", globals::Esp::Snapline::Spotted::Color);

  ImGui::SetNextItemWidth(192.f);
  ImGui::SliderFloat("Thickness##Snapline", &globals::Esp::Snapline::thickness, 1.0f, 10.0f, "%.1f");
}

void InfoSettings() {
  ImGui::Checkbox("Enable##Info", &globals::Esp::Info::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Info Color##Info", globals::Esp::Info::Color);

  ImGui::Checkbox("Spotted##Info", &globals::Esp::Info::Spotted::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Info Spotted Color##Info", globals::Esp::Info::Spotted::Color);

  ImGui::Checkbox("Name##Info", &globals::Esp::Info::Name::player);
  ImGui::Checkbox("State##Info", &globals::Esp::Info::state);
  ImGui::Checkbox("Weapon##Info", &globals::Esp::Info::Name::weapon);
  ImGui::Checkbox("Weapon Icon##Info", &globals::Esp::Info::Icon::enabled);
}

void DeathSettings() {
  ImGui::Checkbox("Enable##Death", &globals::Esp::Death::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Death Color##Death", globals::Esp::Death::Color);

  ImGui::PushItemWidth(192.f);
  ImGui::SliderFloat("Duration##Death", &globals::Esp::Death::duration, 0.1f, 2.0f, "%.1f s");
  ImGui::PopItemWidth();

  ImGui::Checkbox("Box##Death", &globals::Esp::Death::Box::enabled);
  ImGui::Checkbox("Skeleton##Death", &globals::Esp::Death::Skeleton::enabled);
  ImGui::Checkbox("Info##Death", &globals::Esp::Death::Info::enabled);
  ImGui::Checkbox("Health##Death", &globals::Esp::Death::Health::enabled);
  ImGui::Checkbox("Armor##Death", &globals::Esp::Death::Armor::enabled);
  ImGui::Checkbox("Snapline##Death", &globals::Esp::Death::Snapline::enabled);
}

void ViewlineSettings() {
  ImGui::Checkbox("Enable##Viewline", &globals::Esp::Viewline::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Viewline Color", globals::Esp::Viewline::Color);

  ImGui::Checkbox("Spotted##Viewline", &globals::Esp::Viewline::Spotted::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Spotted Viewline Color", globals::Esp::Viewline::Spotted::Color);

  ImGui::Checkbox("Dot##Viewline", &globals::Esp::Viewline::Dot::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Viewline Dot Color", globals::Esp::Viewline::Dot::Color);

  ImGui::PushItemWidth(192.f);

  ImGui::SliderFloat("Length##Viewline", &globals::Esp::Viewline::length, 10.f, 100.f, "%1.f");
}

void DarkmodeSettings() {
  ImGui::Checkbox("Enable##Darkmode", &globals::DarkMode::enabled);
  ImGui::SetNextItemWidth(192.f);
  ImGui::SliderFloat("Alpha##Darkmode", &globals::DarkMode::alpha, 0, 100, "%1.f");
}

void ProjectileSettings() {
  ImGui::Checkbox("Enable##Projectile", &globals::Projectile::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Projectile Color", globals::Projectile::Color);

  ImGui::Checkbox("Box##Projectile", &globals::Projectile::box);
  ImGui::Checkbox("Name##Projectile", &globals::Projectile::name);

  ImGui::Checkbox("Line##Projectile", &globals::Projectile::line);

  // Only show erase option when line is enabled
  if (globals::Projectile::line) {
    ImGui::Checkbox("Erase##Projectile", &globals::Projectile::erase);
  }
}

void SmokeSettings() {
  ImGui::Checkbox("Enable##Smoke", &globals::Smoke::enabled);

  ImGui::Checkbox("Name##Smoke", &globals::Smoke::name::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Name Color##Smoke", globals::Smoke::name::Color);

  ImGui::Checkbox("Circle##Smoke", &globals::Smoke::circle::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Circle Color##Smoke", globals::Smoke::circle::Color);

  ImGui::Checkbox("Countdown##Smoke", &globals::Smoke::countdown::enabled);

  if (globals::Smoke::countdown::enabled)
  {
    ImGui::Checkbox("Bar##Smoke", &globals::Smoke::countdown::bar::enabled);
    ImGui::SameLine(-1.f, 186.f);
    CustomColorEdit4("Bar Color##Smoke", globals::Smoke::countdown::bar::Color);

    if (globals::Smoke::countdown::bar::enabled)
    {
      ImGui::Checkbox("Text##Smoke", &globals::Smoke::countdown::text::enabled);
      ImGui::SameLine(-1.f, 186.f);
      CustomColorEdit4("Text Color##Smoke", globals::Smoke::countdown::text::Color);
    }
  }
}

void NoScopeCrosshairSettings() {
  ImGui::Checkbox("Enable##Noscopecrosshair", &globals::Crosshair::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Crosshair Color", globals::Crosshair::Color);

  ImGui::PushItemWidth(192.f);

  ImGui::SliderFloat("Thickness##Noscopecrosshair", &globals::Crosshair::thickness, 1.f, 10.f, "%1.f");
  ImGui::SliderFloat("Length##Noscopecrosshair", &globals::Crosshair::length, 0.f, 100.f, "%1.f");
  ImGui::SliderFloat("Gap##Noscopecrosshair", &globals::Crosshair::gap, 0.f, 100.f, "%1.f");

  ImGui::Checkbox("Dot##Noscopecrosshair", &globals::Crosshair::dotenabled);
  ImGui::SliderFloat("Dot Size##Noscopecrosshair", &globals::Crosshair::dotSize, 0.f, 100.f, "%1.f");

  ImGui::PopItemWidth();
}

void RecoilCrosshairSettings() {
  ImGui::Checkbox("Enable##RecoilCrosshair", &globals::Misc::RecoilCrosshair::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Recoil Crosshair Color", globals::Misc::RecoilCrosshair::Color);

  ImGui::PushItemWidth(192.f);
  ImGui::SliderFloat("Size##RecoilCrosshair", &globals::Misc::RecoilCrosshair::size, 1.f, 10.f, "%.1f");
  ImGui::PopItemWidth();
}

void WatermarkSettings() {
  ImGui::Checkbox("Enable##Watermark", &globals::Watermark::enabled);

  ImGui::SameLine(-1.f, 152.f);
  CustomColorEdit4("##WatermarkTextColor", globals::Watermark::Color);
  ImGui::SameLine(0.f, 3.f);
  CustomColorEdit4("##WatermarkGradientColor", globals::Watermark::gradientColor);

  ImGui::Checkbox("Nebula##Watermark", &globals::Watermark::showNebula);
  ImGui::Checkbox("FPS##Watermark", &globals::Watermark::showFPS);
  ImGui::Checkbox("RAM##Watermark", &globals::Watermark::showRAM);
  ImGui::Checkbox("BETA##Watermark", &globals::Watermark::showBETA);
}

void HitmarkerSettings() {
  CustomCheckbox("Enable##HitMarker", &globals::Hitmarker::enabled);
  ImGui::SameLine(-1.f, 186.f);
  CustomColorEdit4("Hitmarker Color", globals::Hitmarker::Color);

  ImGui::PushItemWidth(192.f);

  ImGui::SliderFloat("Thickness##HitMarker", &globals::Hitmarker::thickness, 1.f, 10.f, "%1.f");
  ImGui::SliderFloat("Length##HitMarker", &globals::Hitmarker::length, 0.f, 100.f, "%1.f");
  ImGui::SliderFloat("Gap##HitMarker", &globals::Hitmarker::gap, 0.f, 100.f, "%1.f");

  ImGui::SliderFloat("Duration ms##HitMarker", &globals::Hitmarker::duration, 0.f, 100.f, "%1.f");

  ImGui::SliderFloat("flHitmarkerAlpha", &globals::flHitmarkerAlpha, 1.f, 100.f, "%.1f"); //test

  ImGui::PopItemWidth();
}

void SoundSettings() {
  CustomCheckbox("Enable##Sound", &globals::Sound::enabled);

  ImGui::SetNextItemWidth(192.f);

  ImGui::SliderFloat("Volume##Sound", &globals::Sound::volume, 0.f, 100.f, "%1.f");

  CustomCheckbox("Hit##Hit", &globals::Hitsound::enabled);

  ImGui::SetNextItemWidth(150.f);

  char Hitbuffer[256];
  strncpy_s(Hitbuffer, globals::Hitsound::sound.c_str(), sizeof(Hitbuffer));
  if (ImGui::InputText("Sound##Hit", Hitbuffer, sizeof(Hitbuffer))) {
    globals::Hitsound::sound = Hitbuffer;
  }

  ImGui::Dummy(ImVec2(0, 15));

  ImGui::Checkbox("Kill##Kill", &globals::Killsound::enabled);

  ImGui::SetNextItemWidth(150.f);

  char Killbuffer[256];
  strncpy_s(Killbuffer, globals::Killsound::sound.c_str(), sizeof(Killbuffer));
  if (ImGui::InputText("Sound##Kill", Killbuffer, sizeof(Killbuffer))) {
    globals::Killsound::sound = Killbuffer;
  }

  ImGui::Dummy(ImVec2(0, 15));

  ImGui::Checkbox("Hs Kill##HsKill", &globals::HsKillsound::enabled);

  ImGui::SetNextItemWidth(150.f);

  char HsKillbuffer[256];
  strncpy_s(HsKillbuffer, globals::HsKillsound::sound.c_str(), sizeof(HsKillbuffer));
  if (ImGui::InputText("Sound##HsKill", HsKillbuffer, sizeof(HsKillbuffer))) {
    globals::HsKillsound::sound = HsKillbuffer;
  }
}

void KeystrokesSettings() {
  CustomCheckbox("Enable##Keystrokes", &globals::Keystrokes::enabled);

  ImGui::SameLine(-1.f, 152.f);
  CustomColorEdit4("Keystrokes Color", globals::Keystrokes::Color);
  ImGui::SameLine(0.f, 3.f);
  CustomColorEdit4("Keystrokes Pressed Color", globals::Keystrokes::pressedColor);

  ImGui::PushItemWidth(192.f);
  ImGui::SliderFloat("Scale##Keystrokes", &globals::Keystrokes::scale, 0.3f, 1.0f, "%.1f");
  ImGui::PopItemWidth();
}

// Hilfsfunktion zur �berpr�fung, ob ein String einen bestimmten Suchbegriff enth�lt (Fall-unsensitiv)
bool ContainsSearchTerm( const std::string& text, const std::string& searchTerm ) {
  std::string lowerText = text;
  std::transform( lowerText.begin(), lowerText.end(), lowerText.begin(), ::tolower );
  return lowerText.find( searchTerm ) != std::string::npos;
}

// Funktion zur Anzeige der ImGui-Suchleiste und Steuerung der Widget-Sichtbarkeit
void ShowSearchBar() {
  static char searchInput[256] = "";
  ImGui::InputText( "Search", searchInput, sizeof( searchInput ) );

  ImGui::Dummy( ImVec2( 5.f, 5.f ) );

  std::string searchStr = searchInput;
  std::transform( searchStr.begin(), searchStr.end(), searchStr.begin(), ::tolower );

  // �berpr�fe den Suchbegriff und setze die Sichtbarkeitsflags entsprechend
  if ( !searchStr.empty() ) {
    if ( ContainsSearchTerm( "triggerbot", searchStr ) ) {
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      ImGui::Checkbox("Triggerbot##search", &globals::Triggerbot::enabled);
      ImGui::SameLine();
      ImGui::Hotkey(&globals::Triggerbot::hotkey);
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    }
    if ( ContainsSearchTerm( "aimbotlegitbot", searchStr ) ) {
      bool aimbotEnabled = false;
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      ImGui::Checkbox( "Aimbot", &aimbotEnabled );
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    }
    if ( ContainsSearchTerm( "box", searchStr ) ) {
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      ImGui::Text( "Box" );
      ImGui::Separator();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      BoxSettings();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    }
    if ( ContainsSearchTerm( "skeleton", searchStr ) ) {
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      ImGui::Text( "Skeleton" );
      ImGui::Separator();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      SkeletonSettings();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    }
    if ( ContainsSearchTerm( "snapline", searchStr ) ) {
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      ImGui::Text( "Snapline" );
      ImGui::Separator();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      SnaplineSettings();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    }
    if ( ContainsSearchTerm( "info", searchStr ) ) {
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      ImGui::Text( "Info" );
      ImGui::Separator();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      InfoSettings();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    }
    if ( ContainsSearchTerm( "darkmode", searchStr ) ) {
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      ImGui::Text( "Darkmode" );
      ImGui::Separator();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      DarkmodeSettings();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    }
    if ( ContainsSearchTerm( "hitmarker", searchStr ) ) {
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      ImGui::Text( "Hitmarker" );
      ImGui::Separator();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      HitmarkerSettings();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    }
    if ( ContainsSearchTerm( "hitsound", searchStr ) ) {
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      ImGui::Text( "Hitsound" );
      ImGui::Separator();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      SoundSettings();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    }
    if ( ContainsSearchTerm( "noscopecrosshair", searchStr ) ) {
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      ImGui::Text( "Noscope Crosshair" );
      ImGui::Separator();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      NoScopeCrosshairSettings();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    }
    if ( ContainsSearchTerm( "keystrokes", searchStr ) ) {
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      ImGui::Text( "Keystrokes" );
      ImGui::Separator();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      KeystrokesSettings();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    }
    if ( ContainsSearchTerm( "other", searchStr ) ) {
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      ImGui::Text( "Darkmode" );
      ImGui::Separator();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
      DarkmodeSettings();
      ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    }

    // F�ge hier weitere �berpr�fungen f�r zus�tzliche Suchbegriffe hinzu
  }
}

int ToggleGUI( const HWND overlayGui, INT cmd_show ) {
  if ( GetAsyncKeyState( VK_HOME ) ) {
    insertPressed = true;
  } else if ( insertPressed ) {
    insertPressed  = false;
    overlayVisible = !overlayVisible;
    if ( overlayVisible ) {
      ShowWindow( overlayGui, cmd_show );
      UpdateWindow( overlayGui );
    } else {
      ShowWindow( overlayGui, SW_HIDE );
    }
  }

  return 0;
}

int styleUI() {
  ImGuiIO& io = ::ImGui::GetIO();

  ImGuiStyle* Style = &ImGui::GetStyle();

  Style->WindowRounding    = 0.f;
  Style->ChildRounding     = 0.f;
  Style->FrameBorderSize   = 0.f;
  Style->WindowBorderSize  = 1.f;
  Style->ScrollbarSize     = 0.f;
  Style->ScrollbarRounding = 0.f;

  Style->Colors[ImGuiCol_Text]             = ImColor( 255, 255, 255 );
  Style->Colors[ImGuiCol_Header]           = ImColor( 0.35f );
  Style->Colors[ImGuiCol_HeaderHovered]    = ImColor( 0.4f );
  Style->Colors[ImGuiCol_PopupBg]          = ImColor( 10, 10, 10 );
  Style->Colors[ImGuiCol_WindowBg]         = ImColor( 0, 0, 0, 0 );
  Style->Colors[ImGuiCol_ChildBg]          = ImColor( 31, 31, 31 );
  Style->Colors[ImGuiCol_CheckMark]        = ImColor( 255, 255, 255, 255 );
  Style->Colors[ImGuiCol_TextSelectedBg]   = ImColor( 255, 255, 255, 50 );
  Style->Colors[ImGuiCol_FrameBg]          = ImColor( 22, 22, 22 );
  Style->Colors[ImGuiCol_ScrollbarBg]      = ImColor( 14, 14, 14 );
  Style->Colors[ImGuiCol_SliderGrabActive] = ImColor( 1.f );
  Style->Colors[ImGuiCol_FrameBgHovered]   = ImColor( 0.4f );
  Style->Colors[ImGuiCol_FrameBgActive]    = ImColor( 0.2f );
  Style->Colors[ImGuiCol_HeaderActive]     = ImColor( 0.2f );
  Style->Colors[ImGuiCol_TitleBg]          = ImColor( 0, 0, 0 );
  Style->Colors[ImGuiCol_TitleBgActive]    = ImColor( 0, 0, 0 );
  Style->Colors[ImGuiCol_Border]           = ImColor( 255, 255, 255, 0 );

  // Fix button hover colors to prevent transparency issues
  Style->Colors[ImGuiCol_Button]           = ImColor( 0, 0, 0, 255 );        // Solid black for normal state
  Style->Colors[ImGuiCol_ButtonHovered]    = ImColor( 35, 35, 35, 255 );     // Solid dark gray for hover
  Style->Colors[ImGuiCol_ButtonActive]     = ImColor( 65, 65, 65, 255 );     // Solid lighter gray for active

  // Enhanced Combo Box Styling
  Style->Colors[ImGuiCol_FrameBg]          = ImColor( 25, 25, 25, 255 );     // Dark background for combo
  Style->Colors[ImGuiCol_FrameBgHovered]   = ImColor( 35, 35, 35, 255 );     // Slightly lighter on hover
  Style->Colors[ImGuiCol_FrameBgActive]    = ImColor( 45, 45, 45, 255 );     // Even lighter when active
  Style->Colors[ImGuiCol_PopupBg]          = ImColor( 20, 20, 20, 240 );     // Dark popup background
  Style->Colors[ImGuiCol_Border]           = ImColor( 60, 60, 60, 0 );     // Subtle border
  Style->Colors[ImGuiCol_Separator]        = ImColor( 120, 120, 120, 255 );     // Brighter separator - more visible without outlines
  Style->Colors[ImGuiCol_SeparatorHovered] = ImColor( 140, 140, 140, 255 );     // Hovered separator
  Style->Colors[ImGuiCol_SeparatorActive]  = ImColor( 160, 160, 160, 255 );  // Active separator

  // Selectable items in combo dropdown
  Style->Colors[ImGuiCol_Header]           = ImColor( 40, 40, 40, 255 );     // Selected item background
  Style->Colors[ImGuiCol_HeaderHovered]    = ImColor( 50, 50, 50, 255 );     // Hovered item background
  Style->Colors[ImGuiCol_HeaderActive]     = ImColor( 60, 60, 60, 255 );     // Active item background

  Style->Colors[ImGuiCol_ScrollbarGrab]        = ImColor( 255, 255, 255, 0 );
  Style->Colors[ImGuiCol_ScrollbarGrabActive]  = ImColor( 255, 255, 255, 0 );
  Style->Colors[ImGuiCol_ScrollbarGrabHovered] = ImColor( 255, 255, 255, 0 );
  Style->FramePadding                          = { 4.5, 4.5 };

  return 0;
}

float OffsetOutlinedBox = 100.f;
float AnimationDuration = .88f;  // Dauer der Animation in Sekunden
bool  isAnimating       = false;
bool  resetAnimation    = false;  // Hinzugef�gt, um den Reset-Zustand zu steuern

// Global variables for box animations
static float globalCurrentOffset = 0.f;
static bool globalAnimationStarted = false;

// Global function pointer for createOutlinedBox
std::function<void(const char*, ImVec2, ImVec2, std::function<void()>)> createOutlinedBox;

float box_width;
float box_height;

void EspSettings() {
  ImGuiStyle* Style = &ImGui::GetStyle();

  float window_width    = ImGui::GetWindowWidth();
  float window_height   = ImGui::GetWindowHeight();
  box_width             = ( window_width - 80 ) / 2;
  box_height            = window_height * 0.44f;
  float spacing         = 18;
  float vertical_offset = window_height * 0.02f;

  ImVec2 start_pos    = ImGui::GetCursorScreenPos();
  float  right_offset = 3;

  createOutlinedBox( "Box", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), [&]() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "BoxSettings", ImVec2( box_width, box_height - 10 ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    BoxSettings();
    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  createOutlinedBox( "Snapline", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "SnaplineSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    SnaplineSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  createOutlinedBox( "Skeleton", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "SkeletonSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    SkeletonSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  createOutlinedBox( "Info", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "InfoSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    InfoSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );
}

void EspSettings2() {
  ImGuiStyle* Style = &ImGui::GetStyle();

  float window_width    = ImGui::GetWindowWidth();
  float window_height   = ImGui::GetWindowHeight();
  box_width             = ( window_width - 80 ) / 2;
  box_height            = window_height * 0.44f;
  float spacing         = 18;
  float vertical_offset = window_height * 0.02f;

  ImVec2 start_pos    = ImGui::GetCursorScreenPos();
  float  right_offset = 3;

  createOutlinedBox( "Viewline", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), [&]() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "ViewlineSettings", ImVec2( box_width, box_height - 10 ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    ViewlineSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  createOutlinedBox( "Death", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "DeathSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    DeathSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  createOutlinedBox( "2", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "2EnemySettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    //...EnemySettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  createOutlinedBox( "4", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "4Settings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    //...EnemySettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );
}

void OtherVisualsSettings() {
  ImGuiStyle* Style = &ImGui::GetStyle();

  float window_width    = ImGui::GetWindowWidth();
  float window_height   = ImGui::GetWindowHeight();
  box_width             = ( window_width - 80 ) / 2;
  box_height            = window_height * .44f;
  float spacing         = 18;
  float vertical_offset = window_height * 0.02f;

  ImVec2 start_pos    = ImGui::GetCursorScreenPos();
  float  right_offset = 3;  // Konstanter Wert, um alle Boxen nach rechts zu verschieben

  createOutlinedBox( "Darkmode", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "DarkmodeSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    DarkmodeSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  createOutlinedBox( "Smoke", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "SmokeSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    SmokeSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  createOutlinedBox( "Projectile", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "ProjectileSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    ProjectileSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  createOutlinedBox( "4", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {

    } );
}

void WindowStyle() {
  ImGuiStyle* Style = &ImGui::GetStyle();

  float window_width    = ImGui::GetWindowWidth();
  float window_height   = ImGui::GetWindowHeight();
  box_width             = ( window_width - 80 ) / 2;
  box_height            = window_height * .93f;
  float spacing         = 18;
  float vertical_offset = window_height * 0.02f;

  ImVec2 start_pos    = ImGui::GetCursorScreenPos();
  float  right_offset = 3;  // Konstanter Wert, um alle Boxen nach rechts zu verschieben

  createOutlinedBox( "1", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {

    } );

  createOutlinedBox( "2", ImVec2( start_pos.x + spacing * 2 + box_width + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {

    } );

  createOutlinedBox( "3", ImVec2( start_pos.x + spacing * 3 + box_width * 2 + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {

    } );

  Style->FrameRounding = 0.f;
}

void WindowStyle2() {
  ImGuiStyle* Style = &ImGui::GetStyle();

  float window_width    = ImGui::GetWindowWidth();
  float window_height   = ImGui::GetWindowHeight();
  box_width             = ( window_width - 80 ) / 2;
  box_height            = window_height * .44f;
  float spacing         = 18;
  float vertical_offset = window_height * 0.02f;

  ImVec2 start_pos    = ImGui::GetCursorScreenPos();
  float  right_offset = 3;  // Konstanter Wert, um alle Boxen nach rechts zu verschieben

  createOutlinedBox( "1", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {

    } );

  createOutlinedBox( "4", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {

    } );

  createOutlinedBox( "2", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {

    } );

  createOutlinedBox( "3", ImVec2( start_pos.x + spacing * 3 + box_width * 2 + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {

    } );
}

void WindowStyle3() {
  ImGuiStyle* Style = &ImGui::GetStyle();

  float window_width    = ImGui::GetWindowWidth();
  float window_height   = ImGui::GetWindowHeight();
  box_width             = ( window_width - 80 ) / 2;
  box_height            = window_height * .44f;
  float spacing         = 18;
  float vertical_offset = window_height * 0.02f;

  ImVec2 start_pos    = ImGui::GetCursorScreenPos();
  float  right_offset = 3;  // Konstanter Wert, um alle Boxen nach rechts zu verschieben

  createOutlinedBox( "1", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {

    } );

  createOutlinedBox( "3", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {

    } );

  createOutlinedBox( "2", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {

    } );

  createOutlinedBox( "4", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {

    } );
}

string filename = "Nebula.json";
//string Path     = GetMyDocumentsFolderPath() + "\\Nebula\\cfg\\"; fixlater

// Global variables for popup modal
static char globalConfigNameInputBuffer[256] = "";

void DrawConfigSettings() {
  static bool isSaving    = false;
  static bool isLoading   = false;
  static bool saveSuccess = false;
  static bool loadSuccess = false;
  static char configNameInputBuffer[256] = "";
  static char selectedConfigName[256] = "";
  static int selectedConfigIndex = -1;
  static std::vector<std::string> availableConfigs;
  static bool configsLoaded = false;

  static float statusEndTime = 0.0f;

  float window_width    = ImGui::GetWindowWidth();
  float window_height   = ImGui::GetWindowHeight();
  box_width             = ( window_width - 80 ) / 2;
  box_height            = window_height * .44f; // Standard height matching other tabs
  float spacing         = 18;
  float vertical_offset = window_height * 0.02f;

  ImVec2 start_pos    = ImGui::GetCursorScreenPos();
  float  right_offset = 3;

  auto refreshConfigs = [&]() {
    availableConfigs = ConfigManager::GetAvailableConfigs();
    std::string currentFullSelectedConfig = ConfigManager::GetSelectedConfig();
    selectedConfigIndex = -1;
    for (size_t i = 0; i < availableConfigs.size(); i++) {
      if (availableConfigs[i] == currentFullSelectedConfig) {
        selectedConfigIndex = static_cast<int>(i);
        strncpy_s(selectedConfigName, availableConfigs[i].c_str(), sizeof(selectedConfigName) - 1);
        std::string displayName = availableConfigs[i];
        if (displayName.length() > 5 && displayName.substr(displayName.length() - 5) == ".json") {
            displayName = displayName.substr(0, displayName.length() - 5);
        }
        strncpy_s(configNameInputBuffer, displayName.c_str(), sizeof(configNameInputBuffer) -1 );
        break;
      }
    }
    if (selectedConfigIndex == -1 && !availableConfigs.empty()) {
        selectedConfigIndex = 0;
        strncpy_s(selectedConfigName, availableConfigs[0].c_str(), sizeof(selectedConfigName) -1);
        ConfigManager::SetSelectedConfig(availableConfigs[0]);
        std::string displayName = availableConfigs[0];
        if (displayName.length() > 5 && displayName.substr(displayName.length() - 5) == ".json") {
            displayName = displayName.substr(0, displayName.length() - 5);
        }
        strncpy_s(configNameInputBuffer, displayName.c_str(), sizeof(configNameInputBuffer) -1);
    } else if (availableConfigs.empty()) {
        selectedConfigName[0] = '\0';
        configNameInputBuffer[0] = '\0';
        ConfigManager::SetSelectedConfig("");
    }
  };

  if (!configsLoaded) {
    refreshConfigs();
    configsLoaded = true;
  }

  createOutlinedBox( "Config", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), [&]() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "ConfigSettings", ImVec2( box_width -10 , box_height -15), false );

    ImGui::SetCursorPos( ImVec2( 10.f, 5.f ) );
    ImGui::BeginGroup();

    // Config Selection - List-style Combo Box

    // Prepare display names for combo
    std::vector<std::string> displayNames;
    for (const auto& config : availableConfigs) {
        std::string displayName = config;
        if (displayName.length() > 5 && displayName.substr(displayName.length() - 5) == ".json") {
            displayName = displayName.substr(0, displayName.length() - 5);
        }
        displayNames.push_back(displayName);
    }

    // Convert to const char* array for ImGui::Combo
    std::vector<const char*> displayNamesCStr;
    for (const auto& name : displayNames) {
        displayNamesCStr.push_back(name.c_str());
    }

    // Enhanced listbox styling - always visible selection, no label
    ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(12.f, 10.f));  // Better padding for larger items
    ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(8.f, 6.f));     // More spacing between items
    ImGui::PushStyleVar(ImGuiStyleVar_SelectableTextAlign, ImVec2(0.0f, 0.5f)); // Center text vertically

    // Use BeginListBox/EndListBox for custom size - always visible, no dropdown needed, full width, shorter height
    ImVec2 listbox_size = ImVec2(box_width - 20.f, 100.f);  // Full width minus small margin, shorter height
    if (ImGui::BeginListBox("##ConfigListBox", listbox_size)) {
        for (int i = 0; i < static_cast<int>(displayNamesCStr.size()); i++) {
            const bool is_selected = (selectedConfigIndex == i);
            if (ImGui::Selectable(displayNamesCStr[i], is_selected)) {
                selectedConfigIndex = i;
                if (selectedConfigIndex >= 0 && selectedConfigIndex < availableConfigs.size()) {
                    strncpy_s(selectedConfigName, availableConfigs[selectedConfigIndex].c_str(), sizeof(selectedConfigName) - 1);
                    ConfigManager::SetSelectedConfig(availableConfigs[selectedConfigIndex]);
                    strncpy_s(configNameInputBuffer, displayNames[selectedConfigIndex].c_str(), sizeof(configNameInputBuffer) - 1);
                }
            }
            if (is_selected) {
                ImGui::SetItemDefaultFocus();
            }
        }
        ImGui::EndListBox();
    }

    ImGui::PopStyleVar(3);  // Pop all three style vars

    ImGui::SetNextItemWidth(ImGui::GetWindowContentRegionMax().x);
    ImGui::InputText("##ConfigNameInput", configNameInputBuffer, sizeof(configNameInputBuffer));

    ImGui::Dummy(ImVec2(0, 2));

    float twoButtonWidth = (ImGui::GetWindowContentRegionMax().x - 2.0f) / 2.0f;
    if (ImGui::Button("Save", ImVec2(twoButtonWidth, 35))) { // Slightly reduced height
        if (strlen(configNameInputBuffer) > 0) {
            // Copy the config name to global buffer for the popup
            strncpy_s(globalConfigNameInputBuffer, configNameInputBuffer, sizeof(globalConfigNameInputBuffer) - 1);
            ImGui::OpenPopup("Save Config?");
        }
    }

    // Handle the Save Config popup
    if (ImGui::BeginPopup("Save Config?")) {
        std::string configToSaveDisplayName = globalConfigNameInputBuffer;
        std::string fullConfigToSave = std::string(globalConfigNameInputBuffer) + ".json";

        ImGui::Text("Save Config ");

        float buttonWidth = 60.0f;
        float spacing = 2.0f; // User prefers 2 pixel spacing between buttons
        float totalWidth = buttonWidth * 2 + spacing;
        float availableWidth = ImGui::GetContentRegionAvail().x;
        float startX = (availableWidth - totalWidth) * 0.5f;
        ImGui::SetCursorPosX(startX);

        // Sure button
        if (AnimatedButton("Sure", ImVec2(buttonWidth, 20), "popup_sure")) {
            if (!fullConfigToSave.empty() && fullConfigToSave != ".json") {
                ConfigManager::SaveConfig(ConfigManager::GetConfigPath(), fullConfigToSave);
                isSaving = true;
                saveSuccess = true;
                statusEndTime = ImGui::GetTime() + 1.5f;
                ConfigManager::SetSelectedConfig(fullConfigToSave);
                refreshConfigs();
            }
            ImGui::CloseCurrentPopup();
        }

        ImGui::SameLine(0.0f, spacing);

        // Cancel button
        if (AnimatedButton("Cancel", ImVec2(buttonWidth, 20), "popup_cancel")) {
            ImGui::CloseCurrentPopup();
        }

        ImGui::EndPopup();
    }
    ImGui::SameLine(0.0f, 2.0f);
    if (ImGui::Button("Load", ImVec2(twoButtonWidth, 35))) {
      std::string configToLoad = std::string(configNameInputBuffer);
      if (!configToLoad.empty()) {
          std::string fullConfigToLoad = configToLoad + ".json";
          bool found = false;
          for(const auto& cfg : availableConfigs){
              if(cfg == fullConfigToLoad){
                  found = true;
                  break;
              }
          }
          if(found){
            ConfigManager::LoadConfig(ConfigManager::GetConfigPath(), fullConfigToLoad);
            isLoading     = true;
            loadSuccess   = true;
            statusEndTime = ImGui::GetTime() + 1.5f;
            ConfigManager::SetSelectedConfig(fullConfigToLoad);
            refreshConfigs();
          }
      }
    }

    ImGui::Dummy(ImVec2(0, 2));

    if (ImGui::Button("Delete", ImVec2(71, 30))) { // CHANGED TEXT
        if (selectedConfigIndex != -1 && selectedConfigName[0] != '\0') {
            std::string configToDelete = selectedConfigName;
             if (configToDelete != "Nebula.json" && configToDelete != "settings.json") {
                if (ConfigManager::DeleteConfig(configToDelete)) {
                    refreshConfigs();
                }
            }
        }
    }
    ImGui::SameLine(0.0f, 2.0f);
    if (ImGui::Button("Refresh", ImVec2(71, 30))) {
        refreshConfigs();
    }
    ImGui::SameLine(0.0f, 2.0f);
    if (ImGui::Button("Folder", ImVec2(71, 30))) { // CHANGED TEXT
      std::string configPath = ConfigManager::GetConfigPath();
      ShellExecuteA(nullptr, "open", configPath.c_str(), nullptr, nullptr, SW_SHOWNORMAL);
    }

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );



  createOutlinedBox( "Menu", ImVec2( start_pos.x + spacing * 2 + box_width + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );
    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "MenuSettings", ImVec2( box_width, box_height ) );
    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();
    CustomColorPicker3( "##MenuOutlineColor", globals::MenuOutline::Color, 225.f );
    ImGui::EndGroup();
    ImGui::EndChild();
    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );





  // New Config Dialog is removed, save handles creation.

  // Status Messages (remain the same)
  if ( isSaving ) {
    ImGui::SetNextWindowPos( ImVec2( 10, 10 ) );
    ImGui::SetNextWindowSize( ImVec2( 66, 45 ) );
    ImGui::Begin( "##Status", nullptr, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoScrollbar );
    if ( saveSuccess ) {
      ImGui::Text( "Saved" );
    } else {
      ImGui::Text( "Saving config..." );
    }
    ImGui::End();
    if ( ImGui::GetTime() >= statusEndTime ) {
      isSaving = false;
    }
  }

  if ( isLoading ) {
    ImGui::SetNextWindowPos( ImVec2( 10, 60 ) );
    ImGui::SetNextWindowSize( ImVec2( 66, 45 ) );
    ImGui::Begin( "##LoadStatus", nullptr, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoScrollbar );
    if ( loadSuccess ) {
      ImGui::Text( "Loaded" );
    } else {
      ImGui::Text( "Loading config..." );
    }
    ImGui::End();
    if ( ImGui::GetTime() >= statusEndTime ) {
      isLoading = false;
    }
  }
  // Style->FrameRounding = 0.f; // This was outside, ensure it's handled if needed globally or here.
}

void DrawMiscSettings() {
  ImGuiStyle* Style = &ImGui::GetStyle();

  float window_width    = ImGui::GetWindowWidth();
  float window_height   = ImGui::GetWindowHeight();
  box_width             = ( window_width - 80 ) / 2;
  box_height            = window_height * .44f;
  float spacing         = 18;
  float vertical_offset = window_height * 0.02f;

  ImVec2 start_pos    = ImGui::GetCursorScreenPos();
  float  right_offset = 3;  // Konstanter Wert, um alle Boxen nach rechts zu verschieben

  // 1
  createOutlinedBox( "Hitmarker", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "HitmarkerSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    HitmarkerSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  // 3
  createOutlinedBox( "Noscope Crosshair", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "NoScopeCrosshairSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    NoScopeCrosshairSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  // 2
  createOutlinedBox( "Sound", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "SoundSettings", ImVec2( box_width, box_height - 10 ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    SoundSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  // 4
  createOutlinedBox( "Recoil Crosshair", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "RecoilCrosshairSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    RecoilCrosshairSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );
}

void DrawMiscSettings2() {
  ImGuiStyle* Style = &ImGui::GetStyle();

  float window_width    = ImGui::GetWindowWidth();
  float window_height   = ImGui::GetWindowHeight();
  box_width             = ( window_width - 80 ) / 2;
  box_height            = window_height * .44f;
  float spacing         = 18;
  float vertical_offset = window_height * 0.02f;

  ImVec2 start_pos    = ImGui::GetCursorScreenPos();
  float  right_offset = 3;  // Konstanter Wert, um alle Boxen nach rechts zu verschieben

  // 1
  createOutlinedBox( "Keystrokes", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "KeystrokesSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    KeystrokesSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  // 3
  createOutlinedBox( "3", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "3", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();



    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  // 2 - Watermark
  createOutlinedBox( "Watermark", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "WatermarkSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    WatermarkSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  // 4
  createOutlinedBox( "4", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "4", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();



    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );
}

// createOutlinedBox("1", ImVec2(start_pos.x + spacing + right_offset, start_pos.y + vertical_offset), ImVec2(box_width, box_height), []() {
// createOutlinedBox("2", ImVec2(start_pos.x + spacing * 2 + box_width + right_offset, start_pos.y + vertical_offset), ImVec2(box_width, box_height), []() {
// createOutlinedBox("3", ImVec2(start_pos.x + spacing * 3 + box_width * 2 + right_offset, start_pos.y + vertical_offset), ImVec2(box_width, box_height), []() {

void DrawLegitbot()
{
  ImGuiStyle* Style = &ImGui::GetStyle();

  float window_width    = ImGui::GetWindowWidth();
  float window_height   = ImGui::GetWindowHeight();
  box_width             = ( window_width - 80 ) / 2;
  box_height            = window_height * .44f;
  float spacing         = 18;
  float vertical_offset = window_height * 0.02f;

  ImVec2 start_pos    = ImGui::GetCursorScreenPos();
  float  right_offset = 3;  // Konstanter Wert, um alle Boxen nach rechts zu verschieben

  createOutlinedBox( "Aimbot", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "AimbotSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    AimbotSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  createOutlinedBox( "Visuals", ImVec2( start_pos.x + spacing + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "AimbotCircleSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    AimbotCircleSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  createOutlinedBox( "Triggerbot", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + vertical_offset ), ImVec2( box_width, box_height ), []() {
    ImGui::PushStyleColor( ImGuiCol_ChildBg, IM_COL32( 0, 0, 0, 0 ) );
    ImGui::PushStyleVar( ImGuiStyleVar_ScrollbarSize, 0.f );

    ImGui::Dummy( ImVec2( 5.f, 5.f ) );
    ImGui::BeginChild( "TriggerbotSettings", ImVec2( box_width, box_height ) );  // Adjust size for padding

    ImGui::SetCursorPos( ImVec2( 10.f, 0.f ) );
    ImGui::BeginGroup();

    TriggerbotSettings();

    ImGui::EndGroup();

    ImGui::EndChild();

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();
    } );

  createOutlinedBox( "4", ImVec2( start_pos.x + spacing * 2 + 241 + right_offset, start_pos.y + 244 + vertical_offset ), ImVec2( box_width, box_height ), []() {

    } );
}


// Using extern variables defined in globals_vars.cpp

void pageSwitcher(int& page, string side) // Pass page by reference to modify it
{
  float button_width = 19.0f; // Set your button's width
  float button_height = 60.0f; // Set your button's height

  // Calculate button position for bottom-right corner
  float button_x = ImGui::GetWindowWidth() - button_width;
  float button_y = ImGui::GetWindowHeight() - button_height;

  // Create the button
  ImGui::PushFont( font2 );

  if (side == "LEFT" || side == "BOTH") {
    ImGui::SetCursorPos(ImVec2(5, button_y / 2));
    if (AnimatedButton("<", ImVec2(button_width, button_height), "page_left")) {
      page -= 1;
      resetAnimation = true; // Trigger animation when page changes
    }
  }

  if (side == "RIGHT" || side == "BOTH") {
    ImGui::SetCursorPos(ImVec2(button_x - 5, button_y / 2));
    if (AnimatedButton(">", ImVec2(button_width, button_height), "page_right")) {
      page += 1;
      resetAnimation = true; // Trigger animation when page changes
    }
  }

  if (side != "LEFT" && side != "RIGHT" && side != "BOTH" ){
    printf("Error: pageSwitcher (string side) \n");
  }

  ImGui::PopFont();
}

int renderGUI() {
  return 0;
}

const char* titlelol = "Sigmas";

float animationSpeed = .60f;
float currentStep    = 100.f;
bool  reverseing     = false;  // Gibt an, ob die Animation r�ckw�rts l�uft

// Globale Variable, um die Zeit des letzten Frames zu speichern
std::chrono::steady_clock::time_point lastFrameTime = std::chrono::steady_clock::now();

// Skalierungsfaktor zur Beschleunigung der Animation
const float timeScale = 100.0f;  // Experimentiere mit diesem Wert

void UpdateAnimation( ImVec2 healthBarMin, ImVec2 healthBarMax ) {
  ImDrawList* draw_list = ImGui::GetWindowDrawList();

  // Berechne die Delta-Zeit (in Sekunden)
  auto                         currentFrameTime = std::chrono::steady_clock::now();
  std::chrono::duration<float> deltaTime        = currentFrameTime - lastFrameTime;
  lastFrameTime                                 = currentFrameTime;

  // Anpassung der Animation auf Basis der verstrichenen Zeit und Skalierung
  float animationDelta = animationSpeed * deltaTime.count() * timeScale;

  draw_list->AddRectFilled(
    ImVec2( healthBarMin.x, healthBarMin.y ),
    ImVec2( healthBarMax.x, healthBarMax.y - currentStep * 3.18f ),
    ImColor( 0, 0, 0, 255 ) );

  // �berpr�fen, ob die Animation vorw�rts l�uft
  if ( !reverseing ) {
    currentStep -= animationDelta;

    // Wenn der Fortschritt 0 erreicht, Richtung �ndern
    if ( currentStep <= 0.0f ) {
      currentStep = 0.0f;  // Sicherstellen, dass es nicht unter 0 geht
      reverseing  = true;
      animationSpeed *= 2.0f;  // Geschwindigkeit verdoppeln
    }
  } else {
    currentStep += animationDelta;

    // Wenn der Fortschritt 100 erreicht, Richtung wieder �ndern
    if ( currentStep >= 100.0f ) {
      currentStep = 100.0f;  // Sicherstellen, dass es nicht �ber 100 hinausgeht
      reverseing  = false;
      animationSpeed /= 2.0f;  // Geschwindigkeit wieder auf normal setzen
    }
  }
}

bool isFirstFrame = true;

void renderFPS() {
  static std::chrono::time_point<std::chrono::steady_clock> lastUpdate = std::chrono::steady_clock::now();
  auto                                                      now        = std::chrono::steady_clock::now();

  static float getfps;

  if ( std::chrono::duration_cast<std::chrono::milliseconds>( now - lastUpdate ).count() >= 500 ) {
    lastUpdate = now;
    getfps     = ImGui::GetIO().Framerate;
  }

  ImGuiStyle& style = ImGui::GetStyle();

  if ( isFirstFrame ) {
    ImGui::SetNextWindowPos( ImVec2( 0, 0 ) );
  }

  ImGui::PushStyleColor( ImGuiCol_WindowBg, IM_COL32( 0, 0, 0, 255 ) );
  if ( ImGui::Begin( "##fps", NULL, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoSavedSettings ) ) {
    ImDrawList* draw_list  = ImGui::GetWindowDrawList();
    ImVec2      windowPos  = ImGui::GetWindowPos();
    ImVec2      windowSize = ImGui::GetWindowSize();

    ImGui::BeginChild( "##fps", ImVec2( 80, 20 ), false );

    ImGui::PushFont( font2 );

    ImGui::Text( "FPS: " );
    ImGui::SameLine();
    ImGui::Text( "%1.f", getfps );

    ImGui::SameLine();

    ImGui::PopFont();
    ImGui::EndChild();

    ImGui::End();
    ImGui::PopStyleColor();
  }
}

void renderMemory() {
  ImGuiStyle& style = ImGui::GetStyle();
  ImGui::PushStyleColor(ImGuiCol_WindowBg, IM_COL32(0, 0, 0, 255));

  if ( isFirstFrame ) {
    ImGui::SetNextWindowPos( ImVec2( 0, 35 ) );
    isFirstFrame = false;
  }

  MEMORYSTATUSEX memInfo;
  memInfo.dwLength = sizeof(MEMORYSTATUSEX);
  GlobalMemoryStatusEx(&memInfo);

  DWORDLONG totalPhysMem = memInfo.ullTotalPhys;
  DWORDLONG physMemUsed = totalPhysMem - memInfo.ullAvailPhys;

  if (ImGui::Begin("##memory", NULL, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoSavedSettings)) {
    ImDrawList* draw_list = ImGui::GetWindowDrawList();
    ImVec2      windowPos = ImGui::GetWindowPos();
    ImVec2      windowSize = ImGui::GetWindowSize();
    ImGui::BeginChild("##memory", ImVec2(125, 20), false);
    ImGui::PushFont( font2 );

    ImGui::Text("RAM: ");
    ImGui::SameLine();
    ImGui::Text("%.1f/", physMemUsed / (1024.0 * 1024 * 1024));
    ImGui::SameLine();
    ImGui::Text("%.1f", totalPhysMem / (1024.0 * 1024 * 1024));

    ImGui::PopFont();
    ImGui::EndChild();
    ImGui::End();
    ImGui::PopStyleColor();
  }
}

void renderPreview() {
  ImGuiStyle& style = ImGui::GetStyle();

  ImVec2 nextChildPos = ImVec2( childPos.x + childWindowSize.x + 10.f, childPos.y );
  ImGui::SetNextWindowPos( nextChildPos );
  ImGui::SetNextWindowSize( ImVec2( 200, 400 ), ImGuiCond_Always );
  if ( overlayVisible && currentTabVisuals == 0 && currentTab == 1 ) {
    ImGui::PushStyleColor( ImGuiCol_WindowBg, IM_COL32( 20, 20, 20, 255 ) );
    ImGui::Begin( "preview", nullptr, ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoScrollWithMouse | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoResize );
    ImDrawList* draw_list      = ImGui::GetWindowDrawList();
    ImVec2      windowPos      = ImGui::GetWindowPos();
    ImVec2      windowSize     = ImGui::GetWindowSize();
    float       titleBarHeight = ImGui::GetFrameHeight();

    ImVec2 rectMin = ImVec2( windowPos.x + 30.0f, windowPos.y + titleBarHeight + 30.0f );
    ImVec2 rectMax = ImVec2( windowPos.x + windowSize.x - 30.0f, windowPos.y + windowSize.y - 30.0f );

    // Skeleton
    ImVec2 center       = ImVec2( ( rectMin.x + rectMax.x ) * 0.5f, ( rectMin.y + rectMax.y ) * 0.5f );
    float  figureHeight = ( rectMax.y - rectMin.y ) * 0.8f;
    float  headRadius   = figureHeight * 0.1f;

    // Shift the entire figure upwards
    float upwardShift = figureHeight * 0.05f;

    ImVec2 headCenter = ImVec2( center.x, center.y - figureHeight * 0.4f - upwardShift );

    // Body
    ImVec2 bodyTop    = ImVec2( center.x, headCenter.y + headRadius );
    ImVec2 bodyBottom = ImVec2( center.x, center.y + figureHeight * 0.2f - upwardShift );

    // Shoulders and Arms
    float  shoulderWidth = figureHeight * 0.10f;  // Reduced from 0.15f
    ImVec2 shoulderLeft  = ImVec2( bodyTop.x - shoulderWidth, bodyTop.y + figureHeight * 0.05f );
    ImVec2 shoulderRight = ImVec2( bodyTop.x + shoulderWidth, bodyTop.y + figureHeight * 0.05f );

    // Hips and Legs
    float  hipWidth = figureHeight * 0.08f;  // Reduced from 0.12f
    ImVec2 hipLeft  = ImVec2( bodyBottom.x - hipWidth, bodyBottom.y );
    ImVec2 hipRight = ImVec2( bodyBottom.x + hipWidth, bodyBottom.y );

    // Box
    if ( globals::Esp::Box::enabled ) {
      draw_list->AddRect( rectMin, rectMax, ImColor( globals::Esp::Box::Color ), 0.0f, 15, 1.0f );  // Main box

      if ( globals::Esp::Box::Filled::enabled ) {
        draw_list->AddRectFilledMultiColor( rectMin, rectMax, ImColor( globals::Esp::Box::Filled::Color ), ImColor( globals::Esp::Box::Filled::Color ), ImColor( globals::Esp::Box::Filled::Color2 ), ImColor( globals::Esp::Box::Filled::Color2 ) );
      }

      if ( globals::Esp::Box::outline ) {
        draw_list->AddRect( ImVec2( rectMin.x - 1, rectMin.y - 1 ), ImVec2( rectMax.x + 1, rectMax.y + 1 ), IM_COL32( 0, 0, 0, 255 ), 0.0f, 15, 1.0f );  // Outer outline
        draw_list->AddRect( ImVec2( rectMin.x + 1, rectMin.y + 1 ), ImVec2( rectMax.x - 1, rectMax.y - 1 ), IM_COL32( 0, 0, 0, 255 ), 0.0f, 15, 1.0f );  // Inner outline
      }
    }

    // Health bar
    if ( globals::Esp::Health::Bar::enabled ) {
      float  healthBarWidth  = 3.0f;
      float  healthBarHeight = rectMax.y - rectMin.y;
      ImVec2 healthBarMin    = ImVec2( rectMin.x - 5.f - healthBarWidth, rectMin.y );
      ImVec2 healthBarMax    = ImVec2( healthBarMin.x + healthBarWidth, healthBarMin.y + healthBarHeight );
      draw_list->AddRectFilled( healthBarMin, healthBarMax, ImColor( globals::Esp::Health::Bar::Color ) );
      UpdateAnimation( healthBarMin, healthBarMax );
      draw_list->AddRect( ImVec2( healthBarMin.x - 1, healthBarMin.y - 1 ), ImVec2( healthBarMax.x + 1, healthBarMax.y + 1 ), IM_COL32( 0, 0, 0, 255 ) );
    }

    if ( globals::Esp::Skeleton::enabled ) {
      // Head
      if ( globals::Esp::Skeleton::Head::enabled ) {
        draw_list->AddCircle( headCenter, headRadius, ImColor( globals::Esp::Skeleton::Head::Color ), 0, 1.f );
      }

      draw_list->AddLine( bodyTop, bodyBottom, ImColor( globals::Esp::Skeleton::Color ), 1.f );  // Body

      draw_list->AddLine( shoulderLeft, shoulderRight, ImColor( globals::Esp::Skeleton::Color ), 1.f );  // Shoulder

      // Arms
      draw_list->AddLine( shoulderLeft, ImVec2( shoulderLeft.x - figureHeight * 0.07f, shoulderLeft.y + figureHeight * 0.3f ), ImColor( globals::Esp::Skeleton::Color ), 1.f );
      draw_list->AddLine( shoulderRight, ImVec2( shoulderRight.x + figureHeight * 0.07f, shoulderRight.y + figureHeight * 0.3f ), ImColor( globals::Esp::Skeleton::Color ), 1.f );

      // Hip line
      draw_list->AddLine( hipLeft, hipRight, ImColor( globals::Esp::Skeleton::Color ), 1.f );

      // Legs
      draw_list->AddLine( hipLeft, ImVec2( hipLeft.x - figureHeight * 0.04f, hipLeft.y + figureHeight * 0.4f ), ImColor( globals::Esp::Skeleton::Color ), 1.f );
      draw_list->AddLine( hipRight, ImVec2( hipRight.x + figureHeight * 0.04f, hipRight.y + figureHeight * 0.4f ), ImColor( globals::Esp::Skeleton::Color ), 1.f );
    }

    ImGui::End();
    ImGui::PopStyleColor();
  }
}

// lmao
void Overlay::Render()
{
  ImDrawList* draw;
  ImGuiStyle* Style = &ImGui::GetStyle();

  static auto lastTime   = std::chrono::high_resolution_clock::now();
  static int  frameCount = 0;
  // static float fps        = 0.0f;

  // Update animations every frame
  AnimationManager::Update();

  if ( this->RenderMenu || MenuAnimations::IsMenuAnimating() ) {

    // Assign createOutlinedBox lambda function to global variable
    createOutlinedBox = [&]( const char* title, ImVec2 pos, ImVec2 size, std::function<void()> content ) {
      ImDrawList*  draw_list = ImGui::GetWindowDrawList();

      // Get menu alpha to control box visibility
      float menuAlpha = MenuAnimations::GetMenuAlpha(this->RenderMenu);

      // Check if menu just opened - start animation immediately
      static bool wasMenuOpen = false;

      if (!wasMenuOpen && this->RenderMenu) {
        // Menu just opened - boxes start at final position (no slide during menu fade)
        globalCurrentOffset = 0.0f; // Boxes at final position
        resetAnimation = false; // No slide animation during menu fade
        isAnimating = false;
        globalAnimationStarted = false;
      }

      // Check if menu is closing - keep boxes visible until menu is fully faded out
      if (wasMenuOpen && !this->RenderMenu) {
        // Menu is closing - don't reset boxes yet, let them stay visible
        // They will be reset when menuAlpha reaches 0
      }

      wasMenuOpen = this->RenderMenu;

      // Only animate boxes when menu is visible AND not currently fading (for tab switches)
      if (this->RenderMenu && !MenuAnimations::IsMenuAnimating()) {
        // Menu is fully visible and not fading - allow box slide animations for tab switches
        float AnimationSpeed = OffsetOutlinedBox / AnimationDuration;

        if ( isAnimating ) {
          globalCurrentOffset -= AnimationSpeed * ImGui::GetIO().DeltaTime;
          if ( globalCurrentOffset <= 0.f ) {
            globalCurrentOffset = 0.f;
            isAnimating   = false;  // Animation stoppen
          }
        }

        if ( resetAnimation ) {
          globalCurrentOffset  = OffsetOutlinedBox;
          resetAnimation = false;  // Setze reset zurück, um die Animation erneut zu starten
          isAnimating    = true;
          globalAnimationStarted = false;

          // Clear previous animations and start new ones
          MenuAnimations::ClearAllMenuAnimations();

          // Start fade animations immediately when slide starts (no delay)
          if (!globalAnimationStarted) {
            // Start fade animations for all possible box names with no delay
            MenuAnimations::RegisterElementFadeIn("Box", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Snapline", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Skeleton", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Info", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Viewline", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Death", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Darkmode", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Smoke", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Projectile", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Config", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Menu", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Hitmarker", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Noscope Crosshair", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Sound", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Recoil Crosshair", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Keystrokes", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Aimbot", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Visuals", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Triggerbot", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("Watermark", 0.0f, AnimationDuration);
            // Add numbered boxes for generic layouts
            MenuAnimations::RegisterElementFadeIn("1", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("2", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("3", 0.0f, AnimationDuration);
            MenuAnimations::RegisterElementFadeIn("4", 0.0f, AnimationDuration);
            globalAnimationStarted = true;
          }
        }
      } else {
        // Only reset when menu is completely faded out (alpha <= 0.001)
        if (menuAlpha <= 0.001f) {
          // Menu is fully faded out - now reset box positions
          globalCurrentOffset = OffsetOutlinedBox;
          isAnimating = false;
          globalAnimationStarted = false;
          resetAnimation = false; // Clear any pending reset
        }
        // If menu is still fading (alpha > 0.001), keep boxes in current position
      }

      // Get content fade alpha directly from element animation
      float contentAlpha = MenuAnimations::GetElementFadeAlpha(std::string(title));
      if (contentAlpha <= 0.0f) contentAlpha = 0.0f;
      if (contentAlpha >= 1.0f) contentAlpha = 1.0f;

      // Always use menu alpha for content when menu is opening or closing
      if (MenuAnimations::IsMenuAnimating()) {
        // Menu is animating (opening or closing) - make content fade with menu alpha
        contentAlpha = menuAlpha;
      }

      // Show boxes when menu has some visibility (alpha > 0.001)
      // This keeps boxes visible during menu fade-out until almost completely gone
      float boxVisibilityAlpha = (menuAlpha > 0.001f) ? 1.0f : 0.0f;

      // Combine box visibility with content alpha for smooth transitions
      float finalAlpha = boxVisibilityAlpha * contentAlpha;

      // Only render if there's some visibility
      if (finalAlpha > 0.0f && menuAlpha > 0.001f) {
        ImGui::SetCursorScreenPos( ImVec2( pos.x, pos.y + globalCurrentOffset ) );
        ImGui::BeginGroup();
        ImVec2 text_size    = ImGui::CalcTextSize( title );
        float  title_height = text_size.y;
        float  text_x       = pos.x + ( size.x - text_size.x ) / 10 - 10;

        // Apply fade animation to box background and border
        float boxAlpha = finalAlpha; // Use combined alpha for synchronized fade

        draw_list->AddRectFilledMultiColor(
          ImVec2( pos.x, pos.y + globalCurrentOffset ),
          ImVec2( pos.x + size.x, pos.y + size.y + globalCurrentOffset ),
          IM_COL32( 25, 25, 25, (int)(200 * boxAlpha) ),
          IM_COL32( 20, 20, 20, (int)(150 * boxAlpha) ),
          IM_COL32( 10, 10, 10, (int)(100 * boxAlpha) ),
          IM_COL32( 20, 20, 20, (int)(150 * boxAlpha) ) );

        // Removed outline - no AddRect call

        draw_list->AddText(
          ImVec2( text_x, pos.y - title_height + 5.f + globalCurrentOffset ),
          IM_COL32( 255, 255, 255, (int)(255 * boxAlpha) ),
          title );

        // Apply content fade animation
        ImGui::PushStyleVar(ImGuiStyleVar_Alpha, finalAlpha);
        content();
        ImGui::PopStyleVar();

        ImGui::EndGroup();
      }
    };

    styleUI();

    // Remove all rounding effects
    Style->FrameRounding = 0.f;
    Style->WindowRounding = 0.f;
    Style->ChildRounding = 0.f;
    Style->PopupRounding = 0.f;
    Style->ScrollbarRounding = 0.f;
    Style->GrabRounding = 0.f;
    Style->TabRounding = 0.f;

    // ImVec2 mainWindowSize = ImVec2(800, 600);
    ImVec2 pos;

    // Get menu alpha for fade animation
    float menuAlpha = MenuAnimations::GetMenuAlpha(this->RenderMenu);

    // Only render if alpha is greater than 0.01 (almost invisible threshold)
    if (menuAlpha > 0.01f) {
      // Apply menu alpha to the entire window
      ImGui::PushStyleVar(ImGuiStyleVar_Alpha, menuAlpha);

    if ( ImGui::Begin( "Nebula - Pre-Beta", NULL, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoSavedSettings ) ) {
      // in main gui while to check hotkeys
      pos         = ImGui::GetWindowPos();
      draw        = ImGui::GetWindowDrawList();
      ImVec2 size = ImGui::GetWindowSize();

      static bool waitingForKeyPress = false;

      Style->Colors[ImGuiCol_ChildBg] = ImColor( 255, 255, 255, 0 );

      // Hintergrund um das gesamte Fenster zeichnen
      // draw->AddRectFilled(pos, ImVec2(pos.x + size.x, pos.y + size.y), ImColor(0, 0, 0, 100));

      // Variablen für den kombinierten Glow-Effekt
      ImVec2 menuPos1, menuPos2, menuPos3;
      ImVec2 menuSize1, menuSize2, menuSize3;

      ImGui::BeginChild( "##Back", ImVec2( 914 - 250, 555 ), false );
      {
        Style->Colors[ImGuiCol_ChildBg]       = ImColor( 0, 0, 0 );
        Style->Colors[ImGuiCol_Button]        = ImColor( 0, 0, 0, 255 );        // Solid black
        Style->Colors[ImGuiCol_ButtonHovered] = ImColor( 35, 35, 35, 255 );     // Solid dark gray
        Style->Colors[ImGuiCol_ButtonActive]  = ImColor( 65, 65, 65, 255 );     // Solid lighter gray

        // Add outline for ##Top
        ImGui::SetCursorPos( ImVec2( 2, 2 ) );
        ImVec2 top_pos = ImGui::GetCursorScreenPos();
        ImVec2 top_size( 910 - 249, 45 );
        float  top_offset = 1.f;

        // Speichere die Position und Größe für den kombinierten Glow-Effekt
        menuPos1 = ImVec2(top_pos.x - top_offset, top_pos.y - top_offset);
        menuSize1 = ImVec2(top_size.x + top_offset, top_size.y + top_offset);


        ImGui::BeginChild( "##Top", ImVec2( 910 - 249, 45 ), false );
        {
          ImVec2 originalCursorPos = ImGui::GetCursorPos();

          // Verschiebe den Cursor nur f�r den Text
          ImGui::SetCursorPos( ImVec2( 2, 2 ) );
          ImGui::PushFont( font );
          ImGui::Text( "Nebula" );
          ImGui::PopFont();

          // Füge "Ignore Teammates" Checkbox nur im Visuals Tab hinzu
          if (currentTab == 1 && currentTabVisuals == 0) { // Nur im Visuals Tab anzeigen
            ImGui::SameLine();
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 28.0f); // Etwas mehr Abstand zu "Nebula"
            ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 13.0f); // Vertikal mittig
            CustomCheckbox("Ignore Teammates", &globals::Esp::ignoreTeammates);
          }

          ImGui::SetCursorPos( originalCursorPos );

          ImGui::PushStyleVar( ImGuiStyleVar_ItemSpacing, ImVec2( 0, 0 ) );

          switch ( currentTab ) {
          case 1:

            // Erstelle die Spalten an der urspr�nglichen Position
            ImGui::Columns( 2, nullptr, false );
            ImGui::SetColumnOffset( 0, 28 );

            // Wechsle zur n�chsten Spalte f�r den restlichen Inhalt
            ImGui::NextColumn();

            // ESP und Others Tabs zurück zur ursprünglichen Position
            ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 105.0f); // Normale Position für ESP/Others

            if ( AnimatedButton( "ESP", ImVec2( 100, 45 ), "subtab_esp", currentTabVisuals == 0 ) ) {
              currentTabVisuals = 0;
              resetAnimation    = true;
            }

            ImGui::SameLine();

            if ( AnimatedButton( "Others", ImVec2( 100, 45 ), "subtab_others", currentTabVisuals == 1 ) ) {
              currentTabVisuals = 1;
              resetAnimation    = true;
            }

            break;

          }
        }
      }
      ImGui::EndChild();

      childPos        = ImGui::GetWindowPos();
      childScreenPos  = ImGui::GetCursorScreenPos();
      childWindowSize = ImGui::GetItemRectSize();

      // Style->FramePadding = ImVec2(10, 10); // Setzen des gew�nschten Paddings (x, y)

      ImGui::SetCursorPos( ImVec2( 110, 53 ) );
      ImVec2 window_pos = ImGui::GetCursorScreenPos();
      ImVec2 main_size( 910 - 357, 500 );
      float  offset = 1.f;
      // Zeichne die Outline f�r den Hauptbereich
      //Render::AddRect( window_pos.x - offset, window_pos.y - offset, window_pos.x + main_size.x + offset, window_pos.y + main_size.y + offset, globals::MenuOutline::Color, 0.f, 1.f );
      // Speichere die Position und Größe für den kombinierten Glow-Effekt (Hauptbereich)
      menuPos2 = ImVec2(window_pos.x - offset, window_pos.y - offset);
      menuSize2 = ImVec2(main_size.x + offset, main_size.y + offset);

      ImGui::BeginChild( "##Main", ImVec2( 910 - 357, 500 ), true );
      {
        switch ( currentTab ) {
        case 0:

          switch ( currentTabLegitbot ) {
          case 0:
            DrawLegitbot();
            break;
          case 1:
            break;
          case 2:
            break;
          }
          break;
        case 1:
          switch ( currentTabVisuals ) {
          case 0: {
            switch (EspSettingsPage) {
            case 0:{
              EspSettings();
              pageSwitcher(EspSettingsPage, "RIGHT");
            }
            break;

            case 1:
              EspSettings2();
              pageSwitcher(EspSettingsPage, "LEFT");
              break;
            }
            break;
          }
                break;
          case 1:
            OtherVisualsSettings();
            break;
          }
          break;
        case 2:
          switch ( currentTabMisc ) {
          case 0:
            switch (MiscSettingsPage) {
            case 0:{
              DrawMiscSettings();
              pageSwitcher(MiscSettingsPage, "RIGHT");
            }
                  break;
            case 1:
              DrawMiscSettings2();
              pageSwitcher(MiscSettingsPage, "LEFT");
              break;
            }
            break;
          case 1:
            break;
          case 2:
            break;
          }
          break;
        case 3:
          ShowSearchBar();
          break;
        case 4:
          DrawConfigSettings();
          break;
        }
      }

      ImGui::EndChild();

      ImGui::SetCursorPos( ImVec2( 2, 53 ) );

      ImVec2 tabs_pos = ImGui::GetCursorScreenPos();
      ImVec2 tabs_size( 100, 500 );
      float  tabs_offset = 1.f;

      // Zeichne die Outline f�r den Tab-Bereich
      /*
      Render::AddRect(
        tabs_pos.x - tabs_offset,
        tabs_pos.y - tabs_offset,
        tabs_pos.x + tabs_size.x + tabs_offset,
        tabs_pos.y + tabs_size.y + tabs_offset,
        globals::MenuOutline::Color,
        0.f,
        1.f
      );
      */

      // Speichere die Position und Größe für den kombinierten Glow-Effekt (Tab-Bereich)
      menuPos3 = ImVec2(tabs_pos.x - tabs_offset, tabs_pos.y - tabs_offset);
      menuSize3 = ImVec2(tabs_size.x + tabs_offset, tabs_size.y + tabs_offset);

      // Jetzt zeichnen wir einen einzigen Glow-Effekt, der alle drei Bereiche umfasst
      // Finde die Grenzen aller Rechtecke zusammen
      float minX = min(min(menuPos1.x, menuPos2.x), menuPos3.x);
      float minY = min(min(menuPos1.y, menuPos2.y), menuPos3.y);
      float maxX = max(max(menuPos1.x + menuSize1.x, menuPos2.x + menuSize2.x), menuPos3.x + menuSize3.x);
      float maxY = max(max(menuPos1.y + menuSize1.y, menuPos2.y + menuSize2.y), menuPos3.y + menuSize3.y);

      // Zeichne den kombinierten Glow-Effekt
      ImDrawList* drawList = ImGui::GetBackgroundDrawList();
      ImDrawListFlags originalFlags = drawList->Flags;

      // Enable anti-aliasing for smoother glow effect
      drawList->Flags |= ImDrawListFlags_AntiAliasedLines | ImDrawListFlags_AntiAliasedFill;

      // Verbesserter Glow-Effekt mit stärkerer Transparenzabnahme nach außen
      const int numLayers = 20; // Schichten für einen klareren Effekt mit Anti-Aliasing
      float glowSize = 20.f;
      float rounding = 10.f;

      // Von außen nach innen zeichnen
      for (int i = 0; i < numLayers; i++) {
        // Nicht-lineare Abnahme der Transparenz für einen schöneren Effekt
        float t = (float)i / (float)(numLayers - 1); // 0 = außen, 1 = innen

        // Berechne die Größe dieser Schicht (größer am Anfang, kleiner am Ende)
        float layerSize = glowSize * (1.0f - t);

        // Berechne Alpha mit quadratischer Kurve für stärkere Abnahme nach außen
        float alpha = 0.1f * (t * t); // Quadratische Kurve für schnellere Abnahme

        // Erstelle Farbe mit berechneter Transparenz und Menu-Alpha
        ImVec4 layerColor = ImVec4(globals::MenuOutline::Color.x, globals::MenuOutline::Color.y, globals::MenuOutline::Color.z, alpha * menuAlpha);

        // Zeichne die Glow-Schicht um alle Rechtecke herum
        drawList->AddRectFilled(
          ImVec2(minX - layerSize, minY - layerSize),
          ImVec2(maxX + layerSize, maxY + layerSize),
          ImColor(layerColor),
          rounding + layerSize / 2.0f, 0);
      }

      // Restore original flags
      drawList->Flags = originalFlags;


      ImGui::BeginChild( "##Tabs", ImVec2( 100, 500 ), false );
      {
        ImGui::PushStyleVar( ImGuiStyleVar_ItemSpacing, ImVec2( 0, 0 ) );

        // LegitBot Tab
        if ( AnimatedButton( "Legitbot", ImVec2( 100, 100 ), "tab_legitbot", currentTab == 0 ) ) {
          currentTab     = 0;
          resetAnimation = true;
        }

        // Visual Tab
        if ( AnimatedButton( "Visuals", ImVec2( 100, 100 ), "tab_visuals", currentTab == 1 ) ) {
          currentTab     = 1;
          resetAnimation = true;
        }

        // Misc Tab
        if ( AnimatedButton( "Misc", ImVec2( 100, 100 ), "tab_misc", currentTab == 2 ) ) {
          currentTab     = 2;
          resetAnimation = true;
        }

        // Search Tab
        if ( AnimatedButton( "Search", ImVec2( 100, 100 ), "tab_search", currentTab == 3 ) ) {
          currentTab     = 3;
          resetAnimation = true;
        }

        // Config Tab
        if ( AnimatedButton( "Config", ImVec2( 100, 100 ), "tab_config", currentTab == 4 ) ) {
          currentTab     = 4;
          resetAnimation = true;
        }

        ImGui::PopStyleVar();
      }

      ImGui::EndChild();
    }
    ImGui::EndChild();
  }
  ImGui::End();

  // Pop the menu alpha style
  ImGui::PopStyleVar();

     // End of alpha > 0.0f check

  } // End of RenderMenu || IsMenuAnimating check



}
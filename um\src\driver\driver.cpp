#include "pch.h"
#include "driver.hpp"

namespace driver {

  bool attach_to_process( HANDLE driver_handle, const DWORD pid ) {
    Request r;
    r.process_id = reinterpret_cast<HANDLE>( pid );
    return DeviceIoControl( driver_handle, codes::attach, &r, sizeof( r ), &r, sizeof( r ), nullptr, nullptr );
  }

  std::string read_string( HANDLE driver_handle, const uintptr_t addr, size_t max_length ) {
    std::string result;
    char*       temp_buffer = new char[max_length];
    size_t      i;
    bool        null_found = false;

    for ( i = 0; i < max_length && !null_found; ++i ) {
      temp_buffer[i] = read_memory<char>( driver_handle, addr + i );
      if ( temp_buffer[i] == '\0' ) {
        null_found = true;
      }
    }

    result = std::string( temp_buffer, null_found ? i - 1 : i );
    delete[] temp_buffer;
    return result;
  }

}  // namespace driver

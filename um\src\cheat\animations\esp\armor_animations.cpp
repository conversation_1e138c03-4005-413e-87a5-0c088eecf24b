#include "pch.h"
#include "armor_animations.hpp"

// Static member definition
std::unordered_map<int, float> ArmorAnimations::previousArmorValues;

// ===========================
// MAIN API
// ===========================

float ArmorAnimations::GetAnimatedArmor(int entityId, float targetArmor) {
    // Check if we have an active animation for this entity
    if (AnimationManager::HasActiveArmorAnimation(entityId)) {
        return AnimationManager::GetArmorBarValue(entityId);
    }

    // No animation active, return target value
    return targetArmor;
}

void ArmorAnimations::ResetPlayerArmor(int entityId) {
    AnimationManager::RemovePlayerAnimations(entityId);
    previousArmorValues.erase(entityId);
}

void ArmorAnimations::ResetAllArmorAnimations() {
    AnimationManager::ClearBarAnimations();
    previousArmorValues.clear();
}

// ===========================
// AUTOMATIC REGISTRATION
// ===========================

void ArmorAnimations::UpdateArmorAnimation(int entityId, float targetArmor) {
    // Get the current animated armor value (or previous value if no animation)
    float currentArmor = GetAnimatedArmor(entityId, targetArmor);

    // Check if we have a previous value stored
    auto it = previousArmorValues.find(entityId);
    if (it != previousArmorValues.end()) {
        currentArmor = it->second;
    }

    // Only register animation if values are different
    if (std::abs(currentArmor - targetArmor) > 0.1f) {
        RegisterArmorAnimation(entityId, currentArmor, targetArmor);
    }

    // Store the new target value as the previous value for next frame
    previousArmorValues[entityId] = targetArmor;
}

// ===========================
// INTERNAL HELPERS
// ===========================

void ArmorAnimations::RegisterArmorAnimation(int entityId, float currentArmor, float targetArmor) {
    // Use the centralized animation manager
    AnimationManager::RegisterArmorBar(entityId, currentArmor, targetArmor);
}

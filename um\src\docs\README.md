# Nebula CS2 - Entwickler Dokumentation

Kurze Anleitung wie du neue Features in Nebula implementierst.

## 🎯 Was ist Nebula?

Nebula ist ein CS2 External Cheat mit ESP, Aimbot, Visuals und Misc Features. Das Projekt nutzt:
- **Kernel Driver** für Memory Access
- **DirectX 11 Overlay** für Rendering
- **ImGui** für UI
- **Animation System** für smooth Effects
- **GameData System** für einfachen Game-Zugriff

## 🚀 Neue Features hinzufügen

### 1. Neue Animation
```cpp
// In animation_types.hpp
struct MyAnimationData {
    float startValue, targetValue, currentValue;
    std::chrono::steady_clock::time_point startTime;
    bool isActive;
};

// In animation_manager.hpp
static void RegisterMyAnimation(int id, float start, float target);
static float GetMyAnimationValue(int id);

// In animation_manager.cpp
void AnimationManager::RegisterMyAnimation(int id, float start, float target) {
    MyAnimationData& data = myAnimations[id];
    data.startValue = start;
    data.targetValue = target;
    data.startTime = std::chrono::steady_clock::now();
    data.isActive = true;
}

// In Update() Funktion hinzufügen
UpdateMyAnimations();

// Rendering
if (AnimationManager::IsMyAnimationActive(id)) {
    float value = AnimationManager::GetMyAnimationValue(id);
    // Render mit animiertem Wert
}
```

### 2. Neues ESP Feature
```cpp
// In globals.hpp
namespace globals::Esp::MyFeature {
    inline bool enabled = false;
    inline ImVec4 Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
    inline float size = 10.0f;
}

// In visuals.hpp
void DrawMyFeature(const Player& player);

// In visuals.cpp
void VISUALS::DrawMyFeature(const Player& player) {
    if (!globals::Esp::MyFeature::enabled) return;

    Vector screenPos;
    if (!Vector::world_to_screen(GameData::getViewMatrix(), player.Origin, screenPos)) return;

    Render::DrawText(screenPos.x, screenPos.y, "MyFeature",
                    globals::Esp::MyFeature::Color, espfont);
}

// In RenderESP() hinzufügen
if (globals::Esp::MyFeature::enabled) {
    DrawMyFeature(player);
}

// UI in overlayrender.cpp
ImGui::Checkbox("My Feature", &globals::Esp::MyFeature::enabled);
ImGui::ColorEdit4("Color##MyFeature", (float*)&globals::Esp::MyFeature::Color);
```

### 3. Neue Misc Funktion
```cpp
// In misc.hpp
class Misc {
public:
    static void MyNewFeature();
};

// In misc.cpp
void Misc::MyNewFeature() {
    if (!globals::MyFeature::enabled) return;
    if (!GameData::isInitialized()) return;

    // Feature Implementation
    Vector localPos = GameData::getPlayerPosition(GameData::getLocalPlayerPawn());
    Render::DrawText(100, 100, "My Feature Active",
                    ImVec4(1,1,1,1), espfont);
}

// In RenderMisc() hinzufügen
if (globals::MyFeature::enabled) {
    MyNewFeature();
}
```

### 4. Neue Visual Effects
```cpp
// Custom Crosshair
void DrawCustomCrosshair() {
    float centerX = globals::Screen::width / 2.0f;
    float centerY = globals::Screen::height / 2.0f;

    // Kreuz
    Render::DrawLine(centerX - 10, centerY, centerX + 10, centerY,
                    ImVec4(0,1,0,1), 2.0f);
    Render::DrawLine(centerX, centerY - 10, centerX, centerY + 10,
                    ImVec4(0,1,0,1), 2.0f);
}

// Screen Overlay
void DrawHealthOverlay() {
    int health = GameData::getPlayerHealth(GameData::getLocalPlayerPawn());
    if (health < 30) {
        // Roter Rand bei low HP
        ImVec4 redColor = ImVec4(1,0,0, (30-health)/30.0f * 0.3f);
        Render::DrawFilledBox(0, 0, globals::Screen::width, 20, redColor);
    }
}
```

### 5. Konfiguration erweitern
```cpp
// In config_manager.cpp SaveConfig()
j["MyFeature"]["Enabled"] = globals::MyFeature::enabled;
j["MyFeature"]["Color"] = ColorToJson(globals::MyFeature::Color);
j["MyFeature"]["Size"] = globals::MyFeature::size;

// In LoadConfig()
if (j.contains("MyFeature")) {
    globals::MyFeature::enabled = j["MyFeature"].value("Enabled", false);
    ParseColor(j["MyFeature"]["Color"], globals::MyFeature::Color);
    globals::MyFeature::size = j["MyFeature"].value("Size", 10.0f);
}
```

## 📁 Wichtige Dateien

```
src/
├── cheat/
│   ├── globals.hpp           # Alle Settings hier hinzufügen
│   ├── gamedata.hpp          # Einfacher Game-Zugriff
│   ├── features/visuals/     # ESP Features
│   ├── features/misc/        # Misc Features
│   └── animations/           # Animation System
├── window/overlayrender.cpp  # UI Controls hier
├── config/config_manager.cpp # Save/Load Settings
└── render/render.hpp         # Drawing Funktionen
```

## 🔧 Wichtige Systeme

### GameData - Einfacher Game-Zugriff
```cpp
// Immer zuerst prüfen:
if (!GameData::isInitialized()) return;

// Dann verwenden:
view_matrix_t matrix = GameData::getViewMatrix();
int localTeam = GameData::getLocalTeam();
Vector playerPos = GameData::getPlayerPosition(playerPawn);
```

### Rendering - Drawing Funktionen
```cpp
// ===== TEXT RENDERING =====
// Einfacher Text
Render::Text(x, y, color, "Text", fontSize, font_flags_t::none);

// Text mit Schatten
Render::Text(x, y, color, "Text", fontSize, font_flags_t::dropshadow);

// Text mit Outline
Render::Text(x, y, color, "Text", fontSize, font_flags_t::outline);

// Weapon/Gun Text (spezielle Formatierung)
Render::Gun(x, y, color, "AK-47", fontSize, font_flags_t::outline);

// ===== LINIEN =====
// Normale Linie
Render::Line(x1, y1, x2, y2, color, thickness);

// Anti-Aliased Linie (smoother)
Render::AALine(x1, y1, x2, y2, color, thickness);

// Foreground Linie (über allem)
Render::fLine(x1, y1, x2, y2, color, thickness);

// ===== RECHTECKE/BOXEN =====
// Leere Box (nur Rand)
Render::DrawRect(x, y, width, height, color, rounding, thickness);

// Gefüllte Box
Render::DrawRectFilled(x, y, width, height, color, rounding);

// Multi-Color Box (Gradient oben/unten)
Render::DrawRectFilledMultiColor(x, y, width, height, color1, color2);

// Multi-Color Box (Gradient links/rechts)
Render::DrawRectFilledMultiColorLandR(x, y, width, height, color1, color2);

// ===== KREISE =====
// Leerer Kreis (nur Rand)
Render::Circle(x, y, radius, color, thickness);

// Gefüllter Kreis (Dot)
Render::Dot(x, y, radius, color);

// Anti-Aliased Kreis (smoother)
Render::AACircle(x, y, radius, color, thickness);

// Anti-Aliased gefüllter Kreis
Render::AADot(x, y, radius, color);

// ===== GLOW EFFEKTE =====
// Statischer Glow
Render::DrawRectGlow(x, y, width, height, innerColor, glowColor, glowSize, rounding);

// Animierter Glow (pulsierend)
Render::DrawRectGlowAnimated(x, y, width, height, innerColor, glowColor, glowSize, rounding, pulseSpeed, minAlpha, maxAlpha);
```

### World-to-Screen Projection
```cpp
Vector screenPos;
if (Vector::world_to_screen(GameData::getViewMatrix(), worldPos, screenPos)) {
    // Jetzt kannst du auf screenPos.x, screenPos.y rendern
    Render::Text(screenPos.x, screenPos.y, color, "Text", 14.0f, font_flags_t::outline);
}
```

## 🎨 Render Parameter Erklärung

### Text Parameter:
```cpp
Render::Text(x, y, color, text, fontSize, flag);
//           ↑  ↑  ↑      ↑     ↑         ↑
//           |  |  |      |     |         └─ font_flags_t::none/dropshadow/outline
//           |  |  |      |     └─ Schriftgröße (float)
//           |  |  |      └─ Text String
//           |  |  └─ ImVec4 Farbe (r,g,b,a)
//           |  └─ Y Position
//           └─ X Position
```

### Box Parameter:
```cpp
Render::DrawRect(x, y, width, height, color, rounding, thickness);
//               ↑  ↑  ↑      ↑       ↑      ↑         ↑
//               |  |  |      |       |      |         └─ Rand Dicke
//               |  |  |      |       |      └─ Ecken Rundung (0.0f = eckig)
//               |  |  |      |       └─ ImVec4 Farbe
//               |  |  |      └─ Höhe
//               |  |  └─ Breite
//               |  └─ Y Position (oben links)
//               └─ X Position (oben links)
```

### Farben definieren:
```cpp
// Weiß
ImVec4 white = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);

// Rot
ImVec4 red = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);

// Grün (transparent)
ImVec4 greenTransparent = ImVec4(0.0f, 1.0f, 0.0f, 0.5f);

// Aus globals verwenden
ImVec4 projectileColor = globals::Projectile::Color;
```

## 🔧 Praktische Beispiele

### ESP Text über Spieler:
```cpp
void DrawPlayerName(const Vector& screenPos, const std::string& name) {
    if (!globals::Esp::Info::enabled) return;

    Render::Text(
        screenPos.x,
        screenPos.y - 20.0f,  // 20 Pixel über Spieler
        globals::Esp::Info::Color,
        name,
        16.0f,  // Größere Schrift für Namen
        font_flags_t::outline
    );
}
```

### Health Bar:
```cpp
void DrawHealthBar(const Vector& pos, int health) {
    float barWidth = 50.0f;
    float barHeight = 4.0f;
    float healthPercent = health / 100.0f;

    // Hintergrund (schwarz)
    Render::DrawRectFilled(pos.x, pos.y, barWidth, barHeight,
                          ImVec4(0,0,0,0.8f), 0.0f);

    // Health Bar (grün bis rot)
    ImVec4 healthColor = ImVec4(1.0f - healthPercent, healthPercent, 0.0f, 1.0f);
    Render::DrawRectFilled(pos.x, pos.y, barWidth * healthPercent, barHeight,
                          healthColor, 0.0f);
}
```

### Crosshair:
```cpp
void DrawCustomCrosshair() {
    float centerX = globals::Screen::width / 2.0f;
    float centerY = globals::Screen::height / 2.0f;
    float length = 10.0f;
    float gap = 3.0f;
    ImVec4 color = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);

    // Horizontale Linie (links)
    Render::Line(centerX - gap - length, centerY, centerX - gap, centerY, color, 2.0f);
    // Horizontale Linie (rechts)
    Render::Line(centerX + gap, centerY, centerX + gap + length, centerY, color, 2.0f);
    // Vertikale Linie (oben)
    Render::Line(centerX, centerY - gap - length, centerX, centerY - gap, color, 2.0f);
    // Vertikale Linie (unten)
    Render::Line(centerX, centerY + gap, centerX, centerY + gap + length, color, 2.0f);
}
```

### Radar Dot:
```cpp
void DrawRadarPlayer(const Vector& radarPos, bool isEnemy) {
    ImVec4 color = isEnemy ? ImVec4(1,0,0,1) : ImVec4(0,1,0,1);

    // Gefüllter Punkt
    Render::AADot(radarPos.x, radarPos.y, 3.0f, color);

    // Optional: Rand um Punkt
    Render::AACircle(radarPos.x, radarPos.y, 4.0f, ImVec4(1,1,1,0.8f), 1.0f);
}
```

## ⚠️ Wichtige Regeln

1. **Immer GameData::isInitialized() prüfen** vor Game-Zugriff
2. **Alle Settings in globals.hpp** definieren
3. **UI Controls in overlayrender.cpp** hinzufügen
4. **Config in config_manager.cpp** erweitern
5. **AnimationManager::Update()** in main loop aufrufen

## 🚀 Schnell-Referenz

### Neues Feature Checklist:
- [ ] Settings in `globals.hpp` hinzufügen
- [ ] Drawing Funktion implementieren
- [ ] In main render loop integrieren
- [ ] UI Controls in `overlayrender.cpp`
- [ ] Config save/load erweitern
- [ ] Testen mit F-Keys oder Menu

### Häufige Patterns:
```cpp
// Feature Template
if (!globals::MyFeature::enabled) return;
if (!GameData::isInitialized()) return;

// Daten holen
Vector pos = GameData::getPlayerPosition(playerPawn);

// World to Screen
Vector screenPos;
if (!Vector::world_to_screen(GameData::getViewMatrix(), pos, screenPos)) return;

// Rendern
Render::DrawText(screenPos.x, screenPos.y, "Text",
                globals::MyFeature::Color, espfont);
```

## 🎯 DrawProjectile Feature

Die `DrawProjectile` Funktion ist jetzt vollständig implementiert und zeigt Projektil-Namen über deren Position an:

### Features:
- **Namen anzeigen**: Zeigt saubere Namen wie "HE Grenade", "Smoke", "Flash" etc.
- **Box um Projektil**: Optional kleine Box um die Projektil-Position
- **Linie zum Projektil**: Optional Linie vom Screen-Center zum Projektil
- **Konfigurierbar**: Alle Settings über das Menu steuerbar

### Verwendung:
1. **Menu öffnen**: INSERT drücken
2. **ESP Tab**: Zu "Projectile" Box navigieren
3. **Aktivieren**: "Enable" anklicken
4. **Namen aktivieren**: "Name" Checkbox aktivieren
5. **Optional**: "Box" und "Line" für zusätzliche Visuals

### Unterstützte Projektile:
- HE Grenade (hegrenade)
- Smoke Grenade (smokegrenade)
- Flashbang (flashbang)
- Decoy (decoy)
- Molotov (molotov)
- Incendiary (incgrenade)
- Tactical (tagrenade)

### Code Location:
- **Implementation**: `cheat/features/visuals/visuals.cpp` - `DrawProjectile()` Funktion
- **Settings**: `cheat/globals.hpp` - `globals::Projectile` namespace
- **UI**: `window/overlayrender.cpp` - `ProjectileSettings()` Funktion

Das war's! Jetzt kannst du eigene Features implementieren 🎉
#pragma once
#include "pch.h"

// STL includes
#include <string>
#include <unordered_map>
#include <stdexcept>

// Forward declarations
typedef void CURL;  // Forward declare CURL type

// Structure for storing offsets
struct Offsets
{
  std::unordered_map<std::string, uint32_t> data;

  uint32_t get( const std::string& name ) const {
    auto it = data.find( name );
    if ( it != data.end() ) {
      return it->second;
    }
    throw std::runtime_error( "Offset not found: " + name );
  }
};

// Function prototypes
std::string downloadFile( const std::string& url );
Offsets     parseOffsetsFromString( const std::string& data );
void        useclientOffsets( const Offsets& offsets );
void        useOffsets( const Offsets& offsets );
void        usebuttonOffsets( const Offsets& offsets );
